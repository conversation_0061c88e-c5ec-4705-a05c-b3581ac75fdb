const MANAGEMENT_API = process.env['MANAGEMENT_API'];
const OAUTH_API = process.env['OAUTH_API'];
const CONFIG_URL = process.env['CONFIG_URL'];
const PORT = require('./angular.json').projects['sw-ubo-hub-base'].architect.serve.options.port;

module.exports = [
  {
    context: '/api/config',
    target: `http://localhost:${PORT}`,
    pathRewrite: {'/api/config': CONFIG_URL},
    secure: false
  },
  {
    context: ['/v1'],
    target: MANAGEMENT_API,
    secure: true,
    changeOrigin: true,
  },
  {
    context: ['/oauth'],
    target: `${OAUTH_API}/v1`,
    secure: true,
    changeOrigin: true,
  },
];
