{"name": "sw-ubo-hub-base", "version": "0.2.93", "scripts": {"config": "scripty", "prebuild": "rimraf ./dist", "start": "env-cmd ng serve", "start:prod": "env-cmd ng serve --configuration production", "build": "ng build", "build:prod": "ng build --configuration production --source-map", "test": "ng test", "test:coverage": "ng test --no-watch --no-progress --code-coverage", "lint": "ng lint", "e2e": "ng e2e", "postinstall": "ngcc"}, "private": true, "dependencies": {"@angular/animations": "12.2.16", "@angular/cdk": "12.2.13", "@angular/common": "12.2.16", "@angular/compiler": "12.2.16", "@angular/core": "12.2.16", "@angular/flex-layout": "12.0.0-beta.34", "@angular/forms": "12.2.16", "@angular/material": "12.2.16", "@angular/platform-browser": "12.2.16", "@angular/platform-browser-dynamic": "12.2.16", "@angular/router": "12.2.16", "@auth0/angular-jwt": "5.0.2", "@ngx-formly/core": "5.6.2", "@ngx-formly/material": "5.6.2", "@ngx-translate/core": "13.0.0", "@skywind-group/lib-swui": "^0.1.649", "dexie": "3.0.3", "js-base64": "3.5.2", "lodash.isequal": "4.5.0", "moment": "2.29.1", "moment-timezone": "0.5.32", "ngx-color-picker": "10.0.1", "rxjs": "6.6.3", "tslib": "2.0.0", "zone.js": "0.11.4"}, "devDependencies": {"@angular-devkit/build-angular": "12.2.16", "@angular/cli": "12.2.16", "@angular/compiler-cli": "12.2.16", "@angular/language-service": "12.2.16", "@types/jasmine": "3.10.2", "@types/jasminewd2": "2.0.10", "@types/lodash.isequal": "4.5.5", "@types/node": "14.11.5", "codelyzer": "6.0.0", "env-cmd": "10.1.0", "jasmine-core": "3.10.1", "jasmine-spec-reporter": "7.0.0", "karma": "6.3.4", "karma-chrome-launcher": "3.1.0", "karma-coverage-istanbul-reporter": "3.0.3", "karma-jasmine": "4.0.1", "karma-jasmine-html-reporter": "1.7.0", "protractor": "7.0.0", "rimraf": "3.0.2", "rxjs-tslint": "0.1.8", "scripty": "2.0.0", "tslint": "6.1.3", "typescript": "4.3.5"}}