import { env } from './base';

export interface Environment {
  production: boolean;
  APP_VERSION?: string;
  API_ENDPOINT: string;
}

function gitVersion( { build = 0, git: { revision = '', datetime = '', branch = '' } = {} } ) {
  return `${revision} ${build} ${branch} ${datetime}`;
}

export const main: Environment = {
  production: false,
  APP_VERSION: `${env.version} ${gitVersion(env)}`,
  API_ENDPOINT: '/v1',
};
