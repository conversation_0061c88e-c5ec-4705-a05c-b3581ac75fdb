import { AfterViewInit, Component, <PERSON><PERSON><PERSON>roy, OnInit } from '@angular/core';
import { Subject } from 'rxjs';
import { Meta } from '@angular/platform-browser';
import { SwBrowserTitleService, SwHubInitService, TYPES } from '@skywind-group/lib-swui';
import { RedirectService } from './services/redirect.service';
import { environment } from '../environments/environment';
import { filter, takeUntil } from 'rxjs/operators';

@Component({
  selector: 'sw-hub-base-root',
  template: '<router-outlet></router-outlet>'
})
export class AppComponent implements OnInit, AfterViewInit, OnDestroy {
  private readonly destroyed$ = new Subject<void>();

  constructor( meta: Meta,
               private browserTitleService: SwBrowserTitleService,
               private readonly hubService: SwHubInitService,
               private readonly redirectService: RedirectService ) {
    meta.addTag({ name: 'version', content: environment.APP_VERSION });
  }

  ngOnInit(): void {
    this.browserTitleService.setupTitles();
    this.hubService.navigation$.pipe(
      takeUntil(this.destroyed$)
    ).subscribe(navigation => {
      this.redirectService.set(navigation);
    });
    this.hubService.message$.pipe(
      filter(( { type } ) => type === TYPES.OPEN_HUB),
      takeUntil(this.destroyed$)
    ).subscribe(( { body } ) => {
      this.redirectService.navigate(body);
    });
  }

  ngAfterViewInit(): void {
    this.hubService.init();
  }

  ngOnDestroy(): void {
    this.destroyed$.next();
    this.destroyed$.complete();
  }
}
