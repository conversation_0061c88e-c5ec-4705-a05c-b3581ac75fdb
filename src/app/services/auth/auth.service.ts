import { HttpClient, HttpErrorResponse } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Router } from '@angular/router';
import { TranslateService } from '@ngx-translate/core';
import { SwHubAuthService, SwHubConfigService, SwHubInitService, SwuiNotificationsService } from '@skywind-group/lib-swui';
import { from, Observable, of, throwError } from 'rxjs';
import { catchError, map, shareReplay, switchMap, take, tap } from 'rxjs/operators';

import { environment as env } from '../../../environments/environment';
import { getTwofaType } from '../../components/auth/two-factor/two-factor.model';
import { KeyManager } from '../../lib/key-manager';
import { CaptchaInfo, LoginInfo, PasswordResetData, PasswordResetResponse, UserCredentials, UserLoginRequest } from '../../models/user.model';
import { OAuthLoginRequest, OAuthLoginResponse } from '../../models/oauth.model';

export const ERRCODE_TWOFACTOR_NOT_SET = 715;
export const ERRCODE_PASSWORD_CHANGE_REQUIRED = 739;
export const ERRCODE_TOKEN_ALREADY_EXPIRED = 792;

@Injectable()
export class AuthService extends KeyManager {
  private _isPasswordChangeRequired = false;

  private settingsUrl: string = env.API_ENDPOINT + '/settings';
  private loginUrl: string = env.API_ENDPOINT + '/login';
  private passwordChangeNewUserUrl: string = env.API_ENDPOINT + '/login/password/confirm';
  private passwordChangeExistingUserUrl: string = env.API_ENDPOINT + '/users/{username}/password';

  private secondstepUrl = env.API_ENDPOINT + '/login/secondstep';
  private resetPasswordUrl = env.API_ENDPOINT + '/login/password/reset';
  private refreshCaptchaUrl = env.API_ENDPOINT + '/login/refresh-captcha';
  private logoutUrl = env.API_ENDPOINT + '/logout';
  private twofaUrls = {
    secondstep: this.secondstepUrl, // Logs user in using second step auth data
    authTypes: this.secondstepUrl + '/authtypes', // Gets user authtypes info
    setDefault: this.secondstepUrl + '/setdefault', // Set selected auth type as default one

    // Challenges user with selected auth type on first login auth type selection
    challenge: this.secondstepUrl + '/challenge',
    challengeOnAdd: this.secondstepUrl + '/challenge-on-add', // Challenges user when adding second step auth type

    // Verify received auth code to confirm selected auth type on first auth type selection
    confirm: this.secondstepUrl + '/confirm',
    confirmAdd: this.secondstepUrl + '/confirm-add', // Confirm adding second step auth type
  };
  private passwordChangeToken = '';
  private _passwordChangeName = '';

  constructor( private readonly hubConfig: SwHubConfigService,
               private readonly auth: SwHubAuthService,
               private readonly hubService: SwHubInitService,
               private readonly notifications: SwuiNotificationsService,
               private readonly router: Router,
               private readonly http: HttpClient,
               private readonly translate: TranslateService,
  ) {
    super();
  }

  isLogged(): boolean {
    return this.auth.isLogged();
  }

  get username(): string | undefined {
    return this.auth.username;
  }

  set passwordChangeName( name: string ) {
    this._passwordChangeName = name;
  }

  entitySettings(): Observable<any> {
    return this.http.get(this.settingsUrl)
      .pipe(shareReplay(1));
  }

  authorize( { accessToken, grantedPermissions, twoFactor }: LoginInfo ): void {
    this.auth.setToken(accessToken, grantedPermissions);
    this.hubService.sendLogin(accessToken, grantedPermissions, twoFactor);
  }

  login( data: UserCredentials ): Observable<any> {
    const secretKey = this.keyManagerGetActiveKey();
    if (!secretKey) {
      this.notifications.error('Please provide application token');
      return from(this.router.navigate(['/auth/managekeys']));
    }
    if (this.twoFactorManagerGetToken() !== '') {
      this.twoFactorManagerDeleteConfig();
    }

    const value: UserLoginRequest = {
      username: data.username,
      password: data.password,
      secretKey: secretKey.key
    };
    return this.login$(value).pipe(
      map(( response ) => {
        if (response.hasOwnProperty('token') || response.hasOwnProperty('authToken')) {
          const token = response['token'] || response['authToken'];
          let defaultAuthType = response['defaultAuthType'];
          const userAuthTypes = response['userAuthTypes'] || [defaultAuthType];
          const contactInfo = response['contactInfo'];

          // forced challenge if no default auth type but challenge is required
          const defaultIsAbsent = !defaultAuthType && userAuthTypes && userAuthTypes.length > 0;
          const defaultNotInTypes = defaultAuthType && userAuthTypes && userAuthTypes.indexOf(defaultAuthType) === -1;
          if (defaultIsAbsent || defaultNotInTypes) {
            defaultAuthType = userAuthTypes[0];
            this.twoFactorManagerSetConfig({ token, defaultAuthType, userAuthTypes, contactInfo });

            if (getTwofaType(defaultAuthType).challengeOnChange) {
              this.twofaChallenge({ authType: defaultAuthType, contactInfo: '' }).subscribe(() => {
              });
            }
          } else {
            this.twoFactorManagerSetConfig({ token, defaultAuthType, userAuthTypes, contactInfo });
          }
        }
        return response;
      }),
      catchError(( errorResponse: HttpErrorResponse ) => {
        const error = errorResponse.error;

        if (error.hasOwnProperty('code') && error.code === ERRCODE_TWOFACTOR_NOT_SET) {
          const config = {
            token: error['token'],
            brandAuthTypes: error['brandAuthTypes']
          };

          if ('userHasPhoneNumber' in error) {
            config['userHasPhoneNumber'] = error['userHasPhoneNumber'];
          }

          this.twoFactorManagerSetConfig(config);
          return throwError('Two-Factor setup required');
        } else if (error.hasOwnProperty('code') && error.code === ERRCODE_PASSWORD_CHANGE_REQUIRED) {
          this.passwordChangeToken = error.token;
          this._isPasswordChangeRequired = true;
        }

        return this.handleErrors(errorResponse);
      })
    );
  }

  refreshToken() {
    return this.http.post<LoginInfo>(`${env.API_ENDPOINT}/login/refresh`, {}).pipe(
      catchError(() => {
        return of<LoginInfo>({
          username: undefined,
          accessToken: undefined,
          grantedPermissions: undefined
        });
      }),
      map(( res ) => {
        this.authorize(res);
        return true;
      }),
      take(1),
    );
  }

  changePassword( data: UserCredentials ): Observable<any> {
    const secretKey = this.keyManagerGetActiveKey();
    const body = JSON.stringify({
      username: data.username || this._passwordChangeName,
      password: data.password,
      newPassword: data.newPassword,
      secretKey: secretKey.key,
      token: data.token || this.passwordChangeToken,
    });
    const url = this.getPasswordChangeUrl();
    return this.http.request('POST', url, { body, observe: 'response' }).pipe(
      tap(() => this.notifications.success('Password successfully changed')),
      tap(() => this.router.navigate(['/auth/login'])),
      catchError(( errorResponse: HttpErrorResponse ) => this.handleErrors(errorResponse))
    );
  }

  resetPassword( data: PasswordResetData ): Observable<PasswordResetResponse> {
    return this.http.post<PasswordResetResponse>(this.resetPasswordUrl, data);
  }

  refreshCaptcha(): Observable<CaptchaInfo> {
    return this.http.post<CaptchaInfo>(this.refreshCaptchaUrl, {});
  }

  /**
   * Checking is password change required during first login
   */
  passwordChangeRequired(): boolean {
    return this._isPasswordChangeRequired;
  }

  /**
   * Checking if 2FA SETUP is required for current user
   */
  twoFactorSetupRequired(): boolean {
    const token = this.twoFactorManagerGetToken();
    const brandAuthTypes = this.twoFactorManagerGetBrandTypes();
    return token && token !== '' && brandAuthTypes && brandAuthTypes.length > 0;
  }

  /**
   * Checking if 2FA is required for current user
   */
  twoFactorRequired(): boolean {
    const token = this.twoFactorManagerGetToken();
    const brandAuthTypes = this.twoFactorManagerGetBrandTypes();
    return token && token !== '' && (!brandAuthTypes || brandAuthTypes.length === 0);
  }

  twofaChallengeOnAdd( { authType, contactInfo } ): Observable<Object> {
    const url = this.twofaUrls.challengeOnAdd;
    const body = {
      authType
    };

    if (contactInfo) {
      Object.assign(body, { contactInfo });
    }

    return this.http.post(url, body).pipe(
      map(( response ) => {

        const type = response['authType'];
        const info = response['contactInfo'];

        this.twoFactorManagerSetContactInfo(info);
        this.twoFactorManagerSetAuthType(type);
        return response as Object;
      }),
      catchError(( errorResponse: HttpErrorResponse ) => {
        return this.handleErrors(errorResponse);
      })
    );
  }

  /**
   * Submitting preferred auth type for user if he don't have selected 2fa type (part of 2FA setup)
   */
  twofaChallenge( { authType, contactInfo } ): Observable<Object> {
    const url = this.twofaUrls.challenge;
    const body = {
      authType,
      token: this.twoFactorManagerGetToken()
    };

    if (contactInfo) {
      Object.assign(body, { contactInfo });
    }

    if (!body.token || body.token === '') {
      return throwError('Token is missing');
    }

    return this.http.post(url, body).pipe(
      map(( response ) => {

        const type = response['authType'];
        const info = response['contactInfo'];

        this.twoFactorManagerSetContactInfo(info);
        this.twoFactorManagerSetAuthType(type);
        return response as Object;
      }),
      catchError(( errorResponse: HttpErrorResponse ) => {
        this.twoFactorManagerDeleteConfig();
        return this.handleErrors(errorResponse);
      })
    );
  }

  confirmAddTwoFASetup( { authCode, contactInfo } ): Observable<Object> {
    const url = this.twofaUrls.confirmAdd;
    const authType = this.twoFactorManagerGetAuthType();
    const body = {
      authCode,
      authType
    };

    if (contactInfo) {
      Object.assign(body, { contactInfo });
    }

    const newAuth = this.twoFactorManagerGetConfig();

    return this.http.post(url, body)
      .pipe(
        tap(() => {
          this.twoFactorManagerSetConfig({
            ...newAuth,
            userAuthTypes: [...newAuth.userAuthTypes, authType]
          });
        }),
        catchError(( errorResponse: HttpErrorResponse ) => this.handleErrors(errorResponse))
      );
  }

  twofaSetDefault( authType: string ): Observable<Object> {
    const url = this.twofaUrls.setDefault;
    const body = {
      authType
    };

    return this.http.post(url, body)
      .pipe(
        tap(() => {
          this.twoFactorManagerSetConfig({
            ...this.twoFactorManagerGetConfig(),
            defaultAuthType: authType
          });
        }),
        catchError(( errorResponse: HttpErrorResponse ) => this.handleErrors(errorResponse))
      );
  }

  /**
   * Submitting confirmation code for 2FA setup finish
   */
  confirmTwoFASetup( { authCode, contactInfo }: { authCode: string, contactInfo: string } ): Observable<Object> {
    const url = this.twofaUrls.confirm;
    const body = {
      authCode,
      token: this.twoFactorManagerGetToken(),
      authType: this.twoFactorManagerGetAuthType(),
    };

    if (contactInfo) {
      Object.assign(body, { contactInfo });
    }

    if (!body.token || body.token === '') {
      return throwError('Token is missing');
    }

    return this.http.post(url, body).pipe(
      catchError(( errorResponse: HttpErrorResponse ) => {
        const error = errorResponse.error;

        this.twoFactorManagerDeleteConfig();

        if (error.hasOwnProperty('code') && error.code === ERRCODE_PASSWORD_CHANGE_REQUIRED) {
          this.passwordChangeToken = error.token;
          this.router.navigate(['/auth/changepassword']);
        } else {
          this.router.navigate(['/auth/login']);
        }

        return this.handleErrors(errorResponse);
      })
    );
  }

  /**
   * Submitting Two-Factor Auth Code for login
   */
  submitTwoFACode( authCode: number, authType: string ) {
    const url = this.twofaUrls.secondstep;
    const body = {
      authCode,
      authType,
      token: this.twoFactorManagerGetToken(),
    };

    if (!body.token || body.token === '') {
      return throwError('Token is missing');
    }

    return this.http.post(url, body).pipe(
      catchError(( errorResponse: HttpErrorResponse ) => {
        const error = errorResponse.error;
        this.twoFactorManagerDeleteConfig();

        if (error.hasOwnProperty('code') && error.code === ERRCODE_PASSWORD_CHANGE_REQUIRED) {
          this.passwordChangeToken = error.token;
          this.router.navigate(['/auth/changepassword']);
        } else {
          this.router.navigate(['/auth/login']);
        }

        return this.handleErrors(errorResponse);
      }));
  }

  logout( tokenLogoutRequired = true ): void {
    if (this.isLogged()) {
      if (tokenLogoutRequired) {
        this.http.post(this.logoutUrl, {}).subscribe();
      }
      this.auth.logout();
      this.hubService.sendLogout();
    }
    this.router.navigate(['/auth/login']);
  }

  phoneNumberRequired(): boolean {
    const config = this.twoFactorManagerGetConfig();
    return ('userHasPhoneNumber' in config) && config['userHasPhoneNumber'] === false;
  }

  private login$( data: UserLoginRequest ): Observable<LoginInfo> {
    if (this.hubConfig.oauthClientId) {
      const value: OAuthLoginRequest = {
        ...data,
        scopes: ['roles', 'entityId']
      };
      return this.http.post<OAuthLoginResponse>(
        '/oauth/login',
        JSON.stringify(value),
        {
          headers: {
            'x-client-id': this.hubConfig.oauthClientId
          }
        }
      ).pipe(
        switchMap(( { authorizationCode } ) => this.http.post<LoginInfo>(
          `${env.API_ENDPOINT}/oauth/token`,
          JSON.stringify({ authorizationCode, grantType: 'refreshToken' })
        )),
      );
    }
    return this.http.post<LoginInfo>(this.loginUrl, JSON.stringify(data));
  }

  private getPasswordChangeUrl(): string {
    if (this.username) {
      return this.passwordChangeExistingUserUrl.replace('{username}', this.username);
    } else {
      return this.passwordChangeNewUserUrl;
    }
  }

  private handleErrors( errorResponse: HttpErrorResponse ) {
    console.error(errorResponse);
    const closeDuration = 30000;
    const error = errorResponse.error;

    if (errorResponse.status === 401) {
      const errorCodeHasTranslate: boolean = error.code >= 715 && error.code <= 723;
      const message = errorCodeHasTranslate
                      ? this.translate.get(`TWOFA.error_code${error.code}`)['value'] : error.message;

      this.notifications.error(message, 'Error', undefined, undefined, closeDuration);
      if (error.code === 205) {
        this.logout();
      }
    } else if (errorResponse.status === 400) {
      this.notifications.error(error.message, 'Error', undefined, undefined, closeDuration);
    } else {
      this.notifications.error(`${errorResponse.status} Error: ${errorResponse.statusText}`,
        'Error',
        undefined,
        undefined,
        closeDuration);
    }

    return throwError(error);
  }
}
