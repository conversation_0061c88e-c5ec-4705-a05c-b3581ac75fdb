import { Injectable } from '@angular/core';
import <PERSON>ie from 'dexie';
import { IDexieLoginKey } from '../models/dexie.model';
import { STORAGE_ACCESS_KEY_ACTIVE } from '../app.constants';
import { AppStorage } from '../lib/storage';


@Injectable()
export class DexieService extends Dexie {
  loginKeys: Dexie.Table<IDexieLoginKey, number>;

  constructor() {
    super('base-hub');
    this.version(1).stores({
      loginKeys: '++id, active, key, label',
    });
  }

  keyManagerGetKeys(): any {
    return this.loginKeys.orderBy('label').toArray();
  }

  keyManagerAddKey( data: IDexieLoginKey ): Promise<number> {
    return this.loginKeys.add(data);
  }

  keyManagerRemoveKey( data: IDexieLoginKey ): Promise<void> {
    if (data.key === AppStorage.getItem(STORAGE_ACCESS_KEY_ACTIVE)) {
      AppStorage.removeItem(STORAGE_ACCESS_KEY_ACTIVE);
    }
    return this.loginKeys.delete(data.id);
  }
}
