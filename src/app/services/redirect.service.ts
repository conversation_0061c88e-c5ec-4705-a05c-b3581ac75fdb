import { Injectable } from '@angular/core';
import { Router } from '@angular/router';
import { HubConfigHub, NavigationHistoryBody, SwHubAuthService, SwHubConfigService } from '@skywind-group/lib-swui';

const RESTRICTED_URLS = [
  'auth/managekeys',
  'auth/login',
  'auth/login/two-factor',
  'auth/logout',
  'auth/managekeys',
  'auth/changepassword',
  'auth/access-denied',
  '/auth/twofactorsettings'
];

@Injectable()
export class RedirectService {
  private readonly hubs: { [key: string]: HubConfigHub };
  private url?: string;

  constructor( { hubs }: SwHubConfigService,
               private readonly auth: SwHubAuthService,
               private readonly router: Router ) {
    this.hubs = hubs || {};
  }

  set( navigation: NavigationHistoryBody ) {
    if (navigation && !RESTRICTED_URLS.includes(navigation.url)) {
      const hub = this.hubs[navigation.hub];
      if (hub && this.isAllowed(hub)) {
        this.url = `${hub.url}${navigation.url}`;
      }
    }
  }

  navigate(navigation?: NavigationHistoryBody): void {
    if (navigation) {
      this.set(navigation);
    }
    if (this.url) {
      location.href = this.url;
    } else {
      const hubUrl = this.hubUrl;
      if (hubUrl) {
        location.href = hubUrl;
      } else {
        this.router.navigate(['empty']);
      }
    }
  }

  private get hubUrl(): string | null {
    return Object.values(this.hubs)
      .filter(hub => this.isAllowed(hub))
      .map(( { url } ) => url)
      .shift();
  }

  private isAllowed( { permission }: HubConfigHub ): boolean {
    const permissions = Array.isArray(permission) ? permission : permission ? [permission] : null;
    return permissions ? this.auth.allowedTo(permissions) : true;
  }
}
