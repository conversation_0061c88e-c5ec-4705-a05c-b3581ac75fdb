import { AbstractControl, FormControl, FormGroup, ValidationErrors, ValidatorFn } from '@angular/forms';
import { Injectable } from '@angular/core';

const getControlFromGroup = ( controlName, group ): FormControl => {
  let control;
  if (controlName.indexOf('.') > -1) {
    let name: string[] = controlName.split('.');
    if (name.length === 2) {
      control = (group.controls[name[0]] as FormGroup).controls[name[1]];
    } else {
      throw new Error(`Unsupported control name ${controlName}`);
    }
  } else {
    control = group.controls[controlName];
  }

  return control;
};

@Injectable()
export class ValidationService {

  static creditCardValidator( control ) {
    // Visa, MasterCard, American Express, Diners Club, Discover, JCB
    // tslint ignore:line
    const regexp = new RegExp('^(?:4[0-9]{12}(?:[0-9]{3})?|5[1-5][0-9]{14}|6(?:011|5[0-9][0-9])[0-9]{12}|3[47]' +
      '[0-9]{13}|3(?:0[0-5]|[68][0-9])[0-9]{11}|(?:2131|1800|35\d{3})\d{11})$');
    if (!control.value || control.value.match(regexp)) {
      return null;
    } else {
      return { 'invalidCreditCard': true };
    }
  }

  static passwordValidator( control ) {
    // (?=.*\d)                should contain at least one digit
    // (?=.*[a-z])             should contain at least one lower case
    // (?=.*[A-Z])             should contain at least one upper case
    // [a-zA-Z0-9]{8,}         should contain at least 8 from the mentioned characters
    if (/^(?=.*\d)(?=.*[a-z])(?=.*[A-Z])[0-9a-zA-Z_]{8,}$/.test(control.value)) {
      return null;
    } else {
      return { 'invalidPassword': true };
    }
  }

  static passwordConditions( min: number = 1 ): ValidatorFn {
    return ( control: AbstractControl ): { [key: string]: any } => {
      let errors: any = {};

      if (control.value.length < 8) {
        errors.passwordMinLength = true;
      }

      if (control.value.replace(/\D+/g, '').length < min) {
        errors.passwordContainDigit = { value: min };
      }

      if (control.value.replace(/[^a-z]/g, '').length < min) {
        errors.passwordContainLowercase = { value: min };
      }

      if (control.value.replace(/[^A-Z]/g, '').length < min) {
        errors.passwordContainUppercase = { value: min };
      }

      if (!Object.keys(errors).length) {
        errors = null;
      }

      return errors;
    };
  }

  static passwordEqual( equalsTo ): ValidatorFn {
    return ( control: AbstractControl ): { [key: string]: any } => {
      const password1 = control;
      const password2 = control.root.get(equalsTo);

      if (password1 && password2 && password1.value !== password2.value) {
        return { 'passwordNotEquals': true };
      }

      return null;
    };
  }

  static ipv4AddressValidator( control ) {
    const regexp = /\b(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\b/;
    if (!control.value || control.value.match(regexp)) {
      return null;
    } else {
      return { 'invalidIPv4Address': true };
    }
  }

  static ipv4AddressMaskValidator( control ) {
    // see sandbox https://regex101.com/r/Nt3D75/2
    /* tslint:disable */
    const regexp = /^((?:(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)|\*)\.|)){3}(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)|\*)$/m;
    /* tslint:enable */
    if (!control.value || control.value.match(regexp)) {
      return null;
    } else {
      return { 'invalidIPv4AddressMask': true };
    }
  }

  static phoneNumberValidator( control ) {
    const regexp = /^[\\+]?[0-9]{4,18}$/;
    if (!control.value || control.value.match(regexp)) {
      return null;
    } else {
      return { 'invalidPhoneNumberMask': true };
    }
  }

  static digitsOnlyValidator( control ) {
    const regexp = /^(\d+)$/;

    const value = control.value === null ? '' : control.value.toString();
    if (!value || value.match(regexp)) {
      return null;
    } else {
      return { 'invalidDigitsOnly': true };
    }
  }

  static numbersOnlyValidator( control ) {
    const regexp = /^\d+(\.\d{1,3})?$/; // example 10.142

    const value = control.value === null ? '' : control.value.toString();
    if (!value || value.match(regexp)) {
      return null;
    } else {
      return { 'invalidNumbersOnly': true };
    }
  }

  static latinCharsDigitsSymbols( control ) {
    const regexp = /^([a-z0-9\-_.:]+)$/gi;

    if (!control.value || control.value.match(regexp)) {
      return null;
    } else {
      return { 'invalidLatinCharsDigitsSymbols': true };
    }
  }

  static notEquals( restrictedNumber: number ): ValidatorFn {
    return ( control: AbstractControl ): { [key: string]: any } => {
      return control.value === restrictedNumber ? { 'notEquals': { value: control.value } } : null;
    };
  }

  static notEqualsString( restrictedString: string ): ValidatorFn {
    return ( control: AbstractControl ): { [key: string]: any } => {
      return control.value === restrictedString ? { 'notEqualsString': { value: control.value } } : null;
    };
  }

  static notEqualsPassword( restrictedPassword: string ): ValidatorFn {
    return ( control: AbstractControl ): { [key: string]: any } => {
      return control.value === restrictedPassword ? { 'notEqualsPassword': { value: control.value } } : null;
    };
  }

  static minLength( min: number ): ValidatorFn {
    return ( control: AbstractControl ): { [key: string]: any } => {
      let letterCount = 0;
      if (control.value) {
        letterCount = control.value.toString().replace(/\s+/g, '').length;
      }
      if (letterCount >= min) {
        return null;
      }
      return { 'minLength': { valid: false, min } };
    };
  }

  static validateFirstIfSecondIsSetWithValue( firstControlName: string, secondControlName: string,
                                              secondControlValue: string, checkForZero: boolean = false,
  ): ValidatorFn {
    return ( group: FormGroup ): { [key: string]: any } => {
      const firstControl = getControlFromGroup(firstControlName, group);
      const secondControl = getControlFromGroup(secondControlName, group);

      let secondControlValueMismatch = secondControl.value !== secondControlValue;
      let firstControlValueSet = firstControl.value !== ''
        || (checkForZero && firstControl.value !== '' && firstControl.value !== 0);

      if (secondControlValueMismatch || (!secondControlValueMismatch && firstControlValueSet)) {
        return null;
      }

      return { 'validateFirstIfSecondIsSetWithValue': { valid: false, firstControlName } };
    };
  }

  static entityDomainValidator(): ValidatorFn {
    return ( control: AbstractControl ): ValidationErrors | null => {
      const value = control.value || '';
      const parts = value.split('.').filter(i => i.length);
      const levels = 2;
      let result = null;

      if (value.substr(0, 4) === 'http') {
        result = Object.assign({}, result, { domainHttp: true });
      }

      if (value && parts.length < levels) {
        result = Object.assign({}, result, { domainUrlParts: true });
      }

      return result;
    };
  }

  static fileFormatValidator( control: AbstractControl ): ValidationErrors {
    const value = control.value;
    if (!value || !(value instanceof Error)) {
      return null;
    }
    return { fileFormatNotSupported: true };
  }

  static colorHexValidator( control: AbstractControl ) {

    const regexp = /^(#{1})([0-9A-Fa-f]{6}|[0-9A-Fa-f]{3})$/; // example #000000 || #000

    const value = control.value === null ? '' : control.value.toString();

    if (!value || value.match(regexp)) {
      return null;
    } else {
      return { 'invalidColorHexFormat': false };
    }
  }

}
