import { Base64 } from 'js-base64';
import { DEBUG } from '../app.constants';


export class AppStorage {

  static setItem( key: string, val: any ): void {
    localStorage.setItem(key, AppStorage.encode(val));
  }

  static getItem( key: string ): any {
    return AppStorage.decode(localStorage.getItem(key));
  }

  static removeItem( key: string ): any {
    localStorage.removeItem(key);
  }

  static encode( val: any ): string {
    let res = JSON.stringify(val);
    return DEBUG ? res : Base64.encode(res);
  }

  static decode( val: string ): any {
    if (!val) {
      return;
    }
    try {
      let res = DEBUG ? val : Base64.decode(val);
      return JSON.parse(res);
    } catch (e) {
      console.warn('Can`t decode value');
    }
  }
}
