import { IDexie<PERSON><PERSON><PERSON><PERSON><PERSON>, IDexieTwoFactorConfig, IDexieTwoFactorContactInfo } from '../models/dexie.model';
import { AppStorage } from './storage';
import { STORAGE_ACCESS_KEY_ACTIVE } from '../app.constants';

const TWO_FACTOR_CONFIG_KEY = 'twofa';

export abstract class KeyManager {
  // Key manager
  accessKey: string;

  twoFAToken = '';
  twoFASelectedType = '';

  keyManagerSetActiveKey( val: IDexieLoginKey ): void {
    AppStorage.setItem(STORAGE_ACCESS_KEY_ACTIVE, val.key);
  }

  keyManagerRemoveActiveKey(): void {
    AppStorage.removeItem(STORAGE_ACCESS_KEY_ACTIVE);
  }

  keyManagerGetActiveKey(): IDexieLoginKey {
    this.accessKey = AppStorage.getItem(STORAGE_ACCESS_KEY_ACTIVE);
    return this.accessKey && { key: this.accessKey } as IDexieLoginKey;
  }

  twoFactorManagerGetToken(): string {
    const config: IDexieTwoFactorConfig = this.twoFactorManagerGetConfig();
    if (config && config.hasOwnProperty('token') && config.token !== '') {
      this.twoFAToken = config.token;
    }
    return this.twoFAToken;
  }

  twoFactorManagerGetBrandTypes(): string[] {
    const config: IDexieTwoFactorConfig = this.twoFactorManagerGetConfig();
    let result: string[];

    if (config && 'brandAuthTypes' in config && config.brandAuthTypes.length > 0) {
      result = config.brandAuthTypes;
    }
    return result;
  }

  twoFactorManagerGetAuthType(): string {
    const config: IDexieTwoFactorConfig = this.twoFactorManagerGetConfig();
    if (config && config.hasOwnProperty('defaultAuthType') && config.defaultAuthType !== '') {
      this.twoFASelectedType = config.defaultAuthType;
    }
    return this.twoFASelectedType;
  }

  twoFactorManagerSetAuthType( defaultAuthType: string ) {
    const config: IDexieTwoFactorConfig = this.twoFactorManagerGetConfig();
    if (!config.userAuthTypes?.length && defaultAuthType) {
      config.userAuthTypes = [defaultAuthType];
    }
    Object.assign(config, { defaultAuthType });
    this.twoFactorManagerSetConfig(config);
  }

  twoFactorManagerSetContactInfo( info: IDexieTwoFactorContactInfo ) {
    let config: IDexieTwoFactorConfig = this.twoFactorManagerGetConfig();
    const { contactInfo } = config;

    if (!contactInfo) {
      config = { ...config, contactInfo: info };
    } else {
      const isSMSInfo = 'sms' in info;

      if (isSMSInfo) {
        // need keep pristine phone number untouched with *** mask because sms confirmation should use real number
        const pristinePhoneNumber = 'sms' in config.contactInfo && config.contactInfo.sms.length > 0
          && config.contactInfo.sms.indexOf('*') === -1;

        if (pristinePhoneNumber) {
          return;
        }
      }

      config.contactInfo = { ...config.contactInfo, ...info };
    }

    this.twoFactorManagerSetConfig(config);
  }

  twoFactorManagerGetConfig(): IDexieTwoFactorConfig {
    return AppStorage.getItem(TWO_FACTOR_CONFIG_KEY);
  }

  twoFactorManagerSetConfig( val: IDexieTwoFactorConfig ) {
    AppStorage.setItem(TWO_FACTOR_CONFIG_KEY, val);
  }

  twoFactorManagerDeleteConfig() {
    this.twoFAToken = '';
    this.twoFASelectedType = '';
    AppStorage.removeItem(TWO_FACTOR_CONFIG_KEY);
  }
}
