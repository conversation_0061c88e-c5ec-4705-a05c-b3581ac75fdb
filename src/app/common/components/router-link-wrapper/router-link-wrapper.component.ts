import { Component, Input, <PERSON><PERSON><PERSON>roy, OnInit } from '@angular/core';
import { TranslateService } from '@ngx-translate/core';

@Component({
  // tslint:disable:component-selector
  selector: 'router-link-wrapper',
  template: '{{ prefix }}<a [routerLink]="routerLinkData" [attr.target]="target">{{ linkText }}</a>{{ postfix }}',
})

export class RouterLinkWrapperComponent implements OnInit, OnDestroy {

  @Input() translateConstant: string;
  @Input() routerLinkData: string[];
  @Input() marker = '@';
  @Input() targetBlank = false;

  prefix = '';
  linkText = '';
  postfix = '';

  private sub;

  constructor(
    private translate: TranslateService,
  ) {
    this.subscribeToLangChange();
  }

  ngOnInit() {
    this.parseText();
  }

  get target(): string {
    return this.targetBlank ? '_blank' : '_self';
  }

  ngOnDestroy() {
    this.sub.unsubscribe();
  }

  private parseText() {
    this.translate.get(this.translateConstant).subscribe(
      ( message ) => {
        if (message === '') {
          return;
        }

        const parts: string[] = message.split(this.marker);

        if (message.trim().indexOf(this.marker) === 0) {
          this.linkText = parts[0];
          this.postfix = parts[1] || '';
        } else {
          this.prefix = parts[0];
          this.linkText = parts[1];
          this.postfix = parts[2] || '';
        }
      }
    );
  }

  private subscribeToLangChange() {
    this.sub = this.translate.onLangChange.subscribe(() => this.parseText());
  }
}
