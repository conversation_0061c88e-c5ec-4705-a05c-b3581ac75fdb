import { BrowserModule } from '@angular/platform-browser';
import { BrowserAnimationsModule } from '@angular/platform-browser/animations';
import { APP_INITIALIZER, NgModule } from '@angular/core';

import { AppRoutingModule } from './app.routing';
import { AppComponent } from './app.component';
import { CommonModule } from '@angular/common';
import { hub_config_init, SwBrowserTitleService, SwHubConfigService, SwHubInitModule } from '@skywind-group/lib-swui';
import { HttpClientModule } from '@angular/common/http';
import { RedirectService } from './services/redirect.service';

@NgModule({
  imports: [
    CommonModule,
    BrowserModule,
    BrowserAnimationsModule,
    HttpClientModule,
    AppRoutingModule,
    SwHubInitModule.forRoot({
      name: 'base',
      langs: [
        {
          id: 'en',
          title: 'LANGUAGE.English',
          image: 'img/flags/gb.png',
        },
        {
          id: 'zh',
          dialect: 'zh-cn',
          title: 'LANGUAGE.Chinese',
          image: 'img/flags/zh.png',
        },
      ],
      defaultLang: 'en',
      logo: 'img/logo-skywind.png',
      logoSymbols: 'img/logo-white.png',
      auth: {
        blacklistedRoutes: [
          '/locale/.*',
          '/api/config',
          '/oauth/login',
          '/v1/login',
          '/v1/login/refresh',
          '/v1/login/password/confirm',
          '/v1/login/password/reset',
          '/v1/login/secondstep',
          '/v1/login/secondstep/challenge',
          '/v1/login/secondstep/confirm',
        ]
      }
    }),
  ],
  declarations: [
    AppComponent
  ],
  providers: [
    SwBrowserTitleService,
    SwHubConfigService,
    { provide: APP_INITIALIZER, useFactory: hub_config_init, deps: [SwHubConfigService], multi: true },
    RedirectService
  ],
  bootstrap: [AppComponent]
})
export class AppModule {
}
