import { Component, OnInit } from '@angular/core';
import { TranslateService } from '@ngx-translate/core';
import { SwHubConfigService, SwuiNotificationsService } from '@skywind-group/lib-swui';
import { take } from 'rxjs/operators';
import { AuthService } from '../../../services/auth/auth.service';
import { RedirectService } from '../../../services/redirect.service';
import { getTwofaType, TwofaTypeItem } from '../two-factor/two-factor.model';

export interface TwofaContactInfo {
  sms?: string;
  email?: string;
}

export const STEPS = {
  'list': 'list',
  'select': 'select',
  'confirm': 'confirm',
};

@Component({
  selector: 'sw-two-factor-settings',
  templateUrl: 'two-factor-settings.component.html',
  styleUrls: [
    './two-factor-settings.component.scss',
  ]
})
export class TwoFactorSettingsComponent implements OnInit {
  user: any = this.auth.twoFactorManagerGetConfig();
  entitySettings: any = {};

  stepNames = STEPS;
  step?: string;
  readonly logo: string;

  userAuthTypes: TwofaTypeItem[] = [];
  brandAuthTypes: TwofaTypeItem[] = [];
  defaultAuthType?: TwofaTypeItem;
  selectedAuthType?: TwofaTypeItem;
  contactInfo?: any;
  base64QRCodeURI: string;

  constructor(
    private auth: AuthService,
    private notifications: SwuiNotificationsService,
    private translate: TranslateService,
    private redirectService: RedirectService,
    { logo }: SwHubConfigService
  ) {
    this.logo = logo?.solo ?? 'img/logo-skywind-solo.png';
  }

  ngOnInit() {
    this.subscribeToData();
    this.setListStep();
  }

  addNewType() {
    this.setSelectStep();
  }

  onBackClicked() {
    switch (this.step) {
      case STEPS.select:
        this.selectedAuthType = null;
        this.setListStep();
        break;

      case STEPS.confirm:
        this.setSelectStep();
        break;

      default:
        break;
    }
  }

  onSelectComplete( { type, contactInfo, base64QRCodeURI } ) {
    this.selectedAuthType = type;
    this.contactInfo = contactInfo;
    this.base64QRCodeURI = base64QRCodeURI;
    this.setConfirmStep();
  }

  onConfirmComplete( authType: TwofaTypeItem ) {
    this.translate.get('SETTINGS.successTwofaTypeAdded', authType)
      .subscribe(( message ) => {
        this.notifications.success(message);
      });
    this.userAuthTypes = [...this.userAuthTypes, getTwofaType(authType.name)];
    this.setListStep();
  }

  onCancelStep() {
    this.setListStep();
  }

  saveTypeAsDefault( newDefaultAuthType: string ) {
    this.auth.twofaSetDefault(newDefaultAuthType)
      .subscribe(() => {
        const message = this.translate.instant('SETTINGS.successTwofaMarkedAsDefault', { name: newDefaultAuthType });
        this.notifications.success(message);

        this.redirectService.navigate();
      });
  }

  private initializeAuthTypes() {
    let { defaultAuthType, userAuthTypes, contactInfo } = this.user;
    let { twoFactorAuthSettings: { authOptions } } = this.entitySettings;

    this.contactInfo = contactInfo;
    this.defaultAuthType = getTwofaType(defaultAuthType);
    this.userAuthTypes = userAuthTypes.map(type => getTwofaType(type));
    this.brandAuthTypes = authOptions.map(type => getTwofaType(type));
  }

  private setListStep() {
    this.step = STEPS.list;
  }

  private setSelectStep() {
    this.step = STEPS.select;
  }

  private setConfirmStep() {
    this.step = STEPS.confirm;
  }

  private subscribeToData() {
    this.auth.entitySettings()
      .pipe(take(1))
      .subscribe(settings => {
        this.entitySettings = settings;

        this.initializeAuthTypes();
      });
  }
}
