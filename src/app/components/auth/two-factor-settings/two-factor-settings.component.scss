.factor {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  width: 100%;
  &__wrapper {
    width: 100%;
    max-height: 100%;
    overflow: auto;
  }
  &__dialog {
    position: relative;
    width: 360px;
    min-height: 404px;
    margin: auto;
    padding: 32px 32px 102px;
  }
  &__logo {
    width: 265px;
    padding: 16px;
    margin: 0 auto;
    img {
      display: block;
      width: 100%;
    }
  }
  &__title {
    position: relative;
    font-size: 20px !important;
    text-align: center;
    width: 298px;
  }
  &__link {
    display: block;
    font-size: 14px;
    font-weight: 500;
    color: #585858;
    text-decoration: none;
    transition: color .15s ease-in-out;
    padding: 0 8px;
    border-radius: 4px;
  }
  &__subtitle {
    text-align: center;
    font-size: 16px;
    font-weight: 500;
    color: #585858;
  }
  &__field {
    width: 100%;
  }
  &__actions {
    position: absolute;
    bottom: 32px;
    left: 32px;
    width: calc(100% - 64px);
    display: flex;
    gap: 16px;
    button {
      flex: 1;
    }
  }
  &__message {
    margin-bottom: 16px;
  }
  &__radio {
    display: flex;
    flex-direction: column;
    mat-radio-button {
      margin-bottom: 6px;
      &:last-child {
        margin-bottom: 0;
      }
    }
  }
  &__add {
    margin-top: 8px;
  }
  &__image {
    display: block;
    width: 220px;
    margin: auto;
  }
  &__secret {
    display: flex;
    justify-content: center;
    margin: 0 0 24px;
  }
  &__key-toggle {
    margin: auto;
  }
  &__footer {
    position: absolute;
    bottom: 16px;
    left: 16px;
  }
  &__spinner {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 2;
    display: flex;
    align-items: center;
    justify-content: center;
    background: #fff;
    border-radius: 3px;
  }
}
