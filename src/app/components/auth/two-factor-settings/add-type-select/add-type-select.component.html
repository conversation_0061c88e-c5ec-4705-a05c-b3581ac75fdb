<ng-container *ngIf="!selected">
  <div class="factor__message">{{'TWOFA.subTitle_pleaseSelect' | translate}}</div>
  <mat-radio-group class="factor__radio" [(ngModel)]="checked">
    <mat-radio-button *ngFor="let type of types" [value]="type">
      {{ type.displayName | translate }}
    </mat-radio-button>
  </mat-radio-group>

  <div class="factor__actions">
    <button
      mat-stroked-button
      color="primary"
      class="mat-button-md"
      (click)="onBackClick()">
      <mat-icon>arrow_back</mat-icon>
      {{ 'TWOFA.back' | translate }}
    </button>
    <button
      mat-stroked-button
      color="primary"
      class="mat-button-md"
      [disabled]="!checked"
      (click)="submitCheckedType($event)">
      {{ 'TWOFA.next' | translate }}
      <mat-icon>arrow_forward</mat-icon>
    </button>
  </div>
</ng-container>

<ng-container *ngIf="selected && isGoogle(selected)">
  <div class="factor__message">{{ 'TWOFA.googleAuth_scanThisQRCode' | translate }}</div>
  <img class="factor__image" [src]="base64QRCodeURI" alt/>
  <div *ngIf="stringSecretKey" class="factor__secret">
    <a mat-ripple class="factor__link factor__key-toggle" href (click)="!(showGoogleAuthKey = true)"
       *ngIf="!showGoogleAuthKey">
      {{ 'TWOFA.googleAuth_showStringSecretKey' | translate }}
    </a>
    <div class="factor__key" *ngIf="showGoogleAuthKey">{{ stringSecretKey }}</div>
  </div>

  <div class="factor__actions">
    <button
      mat-stroked-button
      color="primary"
      class="mat-button-md"
      (click)="onBackClick()">
      <mat-icon>arrow_back</mat-icon>
      {{ 'TWOFA.back' | translate }}
    </button>
    <button
      mat-stroked-button
      color="primary"
      class="mat-button-md"
      (click)="confirmSelectedType($event)">
      {{ 'TWOFA.next' | translate }}
      <mat-icon>arrow_forward</mat-icon>
    </button>
  </div>
</ng-container>

<ng-container *ngIf="selected && isSMS(selected)">
  <div class="factor__message">{{ 'TWOFA.sms_pleaseEnterPhoneNumber' | translate }}</div>

  <form [formGroup]="phoneNumberForm.get('contactInfo')">
    <mat-form-field appearance="outline" class="factor__field">
      <mat-label>{{ 'TWOFA.phoneNumber' | translate }}</mat-label>
      <mat-icon matPrefix style="margin-right: 6px">phone</mat-icon>
      <input matInput type="text" formControlName="sms" id="phoneNumber">
      <mat-error>
        <lib-swui-control-messages [control]="phoneNumberForm.get('contactInfo.sms')" [messages]="{}">
        </lib-swui-control-messages>
      </mat-error>
    </mat-form-field>
  </form>
  <div class="factor__actions">
    <button
      mat-stroked-button
      color="primary"
      class="mat-button-md"
      (click)="onBackClick()">
      <mat-icon>arrow_back</mat-icon>
      {{ 'TWOFA.back' | translate }}
    </button>
    <button
      mat-stroked-button
      color="primary"
      class="mat-button-md"
      (click)="confirmSelectedType($event)"
      [disabled]="!phoneNumberForm.valid">
      {{ 'TWOFA.next' | translate }}
      <mat-icon>arrow_forward</mat-icon>
    </button>
  </div>
</ng-container>
