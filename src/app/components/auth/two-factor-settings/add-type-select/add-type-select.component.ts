import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { AbstractControl, FormBuilder, FormControl, FormGroup, Validators } from '@angular/forms';
import { take } from 'rxjs/operators';
import { AuthService } from '../../../../services/auth/auth.service';
import { isEmail, isGoogle, isSMS, TwofaTypeItem } from '../../two-factor/two-factor.model';
import { TwofaContactInfo } from '../two-factor-settings.component';

@Component({
  selector: 'sw-add-type-select',
  templateUrl: 'add-type-select.component.html',
  styleUrls: ['../two-factor-settings.component.scss']
})

export class AddTypeSelectComponent implements OnInit {

  types: TwofaTypeItem[] = [];
  checked!: TwofaTypeItem;

  phoneNumberForm!: FormGroup;
  submitted = false;

  stringSecretKey = '';
  showGoogleAuthKey = false;

  isGoogle = isGoogle;
  isSMS = isSMS;
  isEmail = isEmail;

  @Input() selected!: TwofaTypeItem;
  @Input() base64QRCodeURI =
    'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAANQAAADUCAYAAADk3g0YAAAAAklEQVR4AewaftIAAAqDSURBVO3BQY7';

  @Input() userAuthTypes: TwofaTypeItem[] = [];
  @Input() brandAuthTypes: TwofaTypeItem[] = [];
  @Input() contactInfo!: TwofaContactInfo;

  @Output() selectComplete: EventEmitter<any> = new EventEmitter();
  @Output() selectCancel: EventEmitter<boolean> = new EventEmitter();
  @Output() backClicked: EventEmitter<void> = new EventEmitter();

  constructor(
    private fb: FormBuilder,
    private auth: AuthService
  ) {
    this.initForms();
  }

  ngOnInit() {
    this.setAvailableTypes();
    this.checkForOnlyOneTypeAvailable();
  }

  submitCheckedType( event: Event ) {
    event.preventDefault();
    this.useCheckedType();
  }

  useCheckedType() {
    const canSaveToBackend = !isSMS(this.checked) || (isSMS(this.checked) && this.contactInfo?.sms);

    if (canSaveToBackend) {
      this.saveSelectedType();
    } else {
      this.setSelectedType();
    }
  }

  onBackClick() {
    if (this.selected) {
      this.selected = null;
    } else {
      this.backClicked.emit();
    }
  }

  confirmSelectedType( event: Event ) {
    event.preventDefault();
    this.submitted = true;

    if (!!this.selected && isSMS(this.selected) && !this.contactInfo?.sms) {
      this.saveSmsType();
    } else {
      this.completeSetup();
    }
  }

  get contactInfoGroup(): FormGroup {
    return this.phoneNumberForm.get('contactInfo') as FormGroup;
  }

  private get contactInfoSmsControl(): FormControl {
    return this.phoneNumberForm.get('contactInfo.sms') as FormControl;
  }

  private initForms() {
    this.phoneNumberForm = this.fb.group({
      contactInfo: this.fb.group({
        sms: [
          '', Validators.compose([
            Validators.required,
            this.phoneNumberValidator,
          ])
        ]
      })
    });
  }

  /**
   * Set checked type as selected in component only
   */
  private setSelectedType( contactInfo?: any ) {
    this.selected = this.checked;

    const isSetupComplete: boolean = isEmail(this.selected)
      || (isSMS(this.selected) && !!this.contactInfo?.sms);

    if (isSetupComplete) {
      this.completeSetup(contactInfo);
    }
  }

  private checkForOnlyOneTypeAvailable() {
    if (this.types.length === 1) {
      this.checked = this.types[0];
      this.useCheckedType();
    }
  }

  private completeSetup( contactInfo?: TwofaContactInfo ) {
    this.selectComplete.emit({ type: this.selected, contactInfo, base64QRCodeURI: this.base64QRCodeURI });
  }

  /**
   * Saving checked type as selected to backend
   */
  private saveSelectedType() {
    const data = { authType: this.checked.name, contactInfo: '' };
    this.auth.twofaChallengeOnAdd(data).subscribe(
      ( response: any ) => {
        this.grabGoogleAuthParams(response);
        this.setSelectedType(response.contactInfo);
      },
      () => this.selectCancel.emit(true)
    );
  }

  /**
   * Saving sms type as selected to backend if user is required to enter phone number while setup
   */
  private saveSmsType() {
    if (this.phoneNumberForm.valid) {
      const data = {
        authType: this.checked.name,
        contactInfo: this.contactInfoSmsControl.value
      };

      this.auth.twofaChallengeOnAdd(data)
        .pipe(take(1))
        .subscribe(( { contactInfo }: any ) => {
            this.completeSetup(contactInfo);
          },
          () => this.selectCancel.emit(true)
        );
    }
  }

  private grabGoogleAuthParams( response: any ) {
    if (isGoogle(this.checked)) {
      this.base64QRCodeURI = response['totpUri'];
      if ('gaSecretKey' in response) {
        this.stringSecretKey = response['gaSecretKey'];
      }
    }
  }

  private setAvailableTypes() {
    if (!this.brandAuthTypes || this.brandAuthTypes.length === 0) {
      return;
    }

    this.types = this.brandAuthTypes.filter(type => this.userAuthTypes.indexOf(type) === -1);
  }

  private phoneNumberValidator( control: AbstractControl ) {
    const regexp = /^[\\+]?[0-9]{4,18}$/;
    if (!control.value || control.value.match(regexp)) {
      return null;
    } else {
      return { 'invalidPhoneNumberMask': true };
    }
  }
}
