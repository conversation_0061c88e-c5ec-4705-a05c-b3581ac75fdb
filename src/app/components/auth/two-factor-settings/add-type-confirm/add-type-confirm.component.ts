import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { AbstractControl, FormBuilder, FormGroup, Validators } from '@angular/forms';
import { AuthService } from '../../../../services/auth/auth.service';
import { isSMS, TWOFA_TYPE_NAMES, TwofaTypeItem } from '../../two-factor/two-factor.model';
import { TwofaContactInfo } from '../two-factor-settings.component';

@Component({
  selector: 'sw-add-type-confirm',
  templateUrl: 'add-type-confirm.component.html',
  styleUrls: ['../two-factor-settings.component.scss']
})
export class AddTypeConfirmComponent implements OnInit {

  @Input() contactInfo!: TwofaContactInfo;
  @Input() authType!: TwofaTypeItem;
  twofaTypes = TWOFA_TYPE_NAMES;

  submitted = false;
  form!: FormGroup;

  @Output() confirmComplete: EventEmitter<TwofaTypeItem> = new EventEmitter();
  @Output() confirmCancel: EventEmitter<boolean> = new EventEmitter();
  @Output() backClicked: EventEmitter<void> = new EventEmitter();

  constructor(
    private fb: FormBuilder,
    private auth: AuthService
  ) {
  }

  ngOnInit() {
    this.initForm();
  }

  initForm() {
    this.form = this.fb.group({
      'authCode': [
        '', Validators.compose([
          Validators.required,
          this.digitsOnlyValidator
        ])
      ]
    });
  }

  onBackClick() {
    this.backClicked.emit();
  }

  submitForm( event: Event ) {
    event.preventDefault();
    this.submitted = true;

    if (this.form.valid) {
      this.submitConfirmCode(this.form.value);
    }
  }

  submitConfirmCode( data: any ) {
    let contactInfo = '';
    if (this.contactInfo && isSMS(this.authType) && !this.contactInfo.sms) {
      contactInfo = (this.contactInfo as any)[this.authType.name];
    }

    const confirmData = { ...data, authType: this.authType.name, contactInfo };
    this.auth.confirmAddTwoFASetup(confirmData).subscribe(
      () => this.confirmComplete.emit(this.authType),
      () => this.confirmCancel.emit(true)
    );
  }

  private digitsOnlyValidator( control: AbstractControl ) {
    const regexp = /^(\d+)$/;
    const value = control.value === null ? '' : control.value.toString();
    if (!value || value.match(regexp)) {
      return null;
    } else {
      return { 'digitsOnly': true };
    }
  }
}
