<div class="factor__subtitle">{{ 'TWOFA.subTitle_confirmation' | translate }}</div>

<div class="factor__message" [ngSwitch]="authType.name">
  <span *ngSwitchCase="twofaTypes.sms">
    {{ 'TWOFA.sms_pleaseEnterCode' | translate: {number: contactInfo?.sms} }}
  </span>
  <span *ngSwitchCase="twofaTypes.email">
    {{ 'TWOFA.email_pleaseEnterCode' | translate: {email: contactInfo?.email} }}
  </span>
  <span *ngSwitchCase="twofaTypes.google">
    {{ 'TWOFA.googleAuth_pleaseEnterCode' | translate: {email: contactInfo?.email} }}
  </span>
  <span>{{ 'TWOFA.setupConfirm_postfix' | translate }}</span>
</div>

<!--<mat-error *ngIf="errorMsg">{{ errorMsg }}</mat-error>-->

<mat-form-field appearance="outline" class="factor__field">
  <mat-label>{{ 'TWOFA.code' | translate }}</mat-label>
  <mat-icon matPrefix style="margin-right: 6px">security</mat-icon>
  <input matInput type="text" [formControl]="form.get('authCode')">
  <mat-error>
    <lib-swui-control-messages [control]="form.get('authCode')" [force]="submitted" [messages]="{}">
    </lib-swui-control-messages>
  </mat-error>
</mat-form-field>
<div class="factor__actions">
  <button mat-stroked-button
          color="primary"
          class="mat-button-md"
          (click)="onBackClick()">
    {{ 'TWOFA.back' | translate }}
  </button>
  <button
    mat-flat-button
    color="primary"
    (click)="submitForm($event)"
    [disabled]="form.get('authCode').invalid">
    {{ 'TWOFA.submit' | translate }}
  </button>
</div>
