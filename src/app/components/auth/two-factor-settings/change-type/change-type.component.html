<div class="factor__message">{{ 'TWOFA.subTitle_pleaseSelectChange' | translate }}</div>
<mat-radio-group class="factor__radio" [(ngModel)]="newDefaultAuthType">
  <mat-radio-button
    *ngFor="let type of userAuthTypes"
    [value]="type.name">
    {{ type.displayName | translate }}
  </mat-radio-button>
</mat-radio-group>
<button
  *ngIf="canAddNewType()"
  class="factor__add mat-button-md"
  mat-button color="primary"
  (click)="addNewType.emit()">
  <mat-icon>add</mat-icon>
  {{ 'SETTINGS.btnAddNewTwofaType' | translate }}
</button>

<div class="factor__actions">
  <button mat-stroked-button
          color="primary"
          class="mat-button-md"
          (click)="onBackClick()">
    {{ 'SETTINGS.back' | translate }}
  </button>
  <button mat-flat-button color="primary" class="mat-button-md"
          [disabled]="!defaultAuthType || defaultAuthType.name === newDefaultAuthType"
          (click)="saveTypeAsDefault.emit(newDefaultAuthType)">
    {{ 'SETTINGS.apply' | translate }}
  </button>
</div>


