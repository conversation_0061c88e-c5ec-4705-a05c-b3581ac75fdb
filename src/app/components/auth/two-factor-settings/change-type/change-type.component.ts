import { Component, EventEmitter, Input, OnChanges, Output } from '@angular/core';
import { RedirectService } from '../../../../services/redirect.service';
import { TwofaTypeItem } from '../../two-factor/two-factor.model';

@Component({
  selector: 'sw-two-factor-change-type',
  templateUrl: './change-type.component.html',
  styleUrls: ['../two-factor-settings.component.scss']
})
export class ChangeTypeComponent implements OnChanges {
  @Input() userAuthTypes: TwofaTypeItem[] = [];
  @Input() brandAuthTypes: TwofaTypeItem[] = [];
  @Input() defaultAuthType: TwofaTypeItem;

  @Output() addNewType = new EventEmitter<void>();
  @Output() saveTypeAsDefault = new EventEmitter<string>();

  newDefaultAuthType = '';

  constructor( private redirectService: RedirectService ) {
  }

  ngOnChanges(): void {
    this.newDefaultAuthType = this.defaultAuthType?.name;
  }

  canAddNewType(): boolean {
    return this.userAuthTypes.length < this.brandAuthTypes.length;
  }

  onBackClick() {
    this.redirectService.navigate();
  }
}
