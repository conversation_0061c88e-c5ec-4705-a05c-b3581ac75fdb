<div class="factor">
  <div class="factor__wrapper">
    <mat-card class="factor__dialog mat-elevation-z0">
      <div class="factor__logo">
        <img [src]="logo" alt="{{ 'skywindFalconLogoAlt' | translate }}">
      </div>
      <mat-card-header>
        <mat-card-title class="factor__title">
          {{ 'TWOFA.title' | translate }}</mat-card-title>
      </mat-card-header>
      <mat-card-content>
        <ng-container [ngSwitch]="step">
          <div class="types-container" *ngSwitchCase="stepNames.list">
            <sw-two-factor-change-type
              [brandAuthTypes]="brandAuthTypes"
              [userAuthTypes]="userAuthTypes"
              [defaultAuthType]="defaultAuthType"
              (addNewType)="addNewType()"
              (saveTypeAsDefault)="saveTypeAsDefault($event)">
            </sw-two-factor-change-type>
          </div>

          <div class="content-wrapper" *ngSwitchCase="stepNames.select">
            <sw-add-type-select
              [brandAuthTypes]="brandAuthTypes"
              [userAuthTypes]="userAuthTypes"
              [contactInfo]="contactInfo"
              [selected]="selectedAuthType"
              [base64QRCodeURI]="base64QRCodeURI"
              (backClicked)="onBackClicked()"
              (selectComplete)="onSelectComplete($event)"
              (selectCancel)="onCancelStep()"
            ></sw-add-type-select>
          </div>

          <div class="content-wrapper" *ngSwitchCase="stepNames.confirm">
            <sw-add-type-confirm
              [authType]="selectedAuthType"
              [contactInfo]="contactInfo"
              (backClicked)="onBackClicked()"
              (confirmComplete)="onConfirmComplete($event)"
              (confirmCancel)="onCancelStep()">
            </sw-add-type-confirm>
          </div>

        </ng-container>
      </mat-card-content>
    </mat-card>
  </div>
</div>
