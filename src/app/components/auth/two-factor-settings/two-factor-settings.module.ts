import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatCardModule } from '@angular/material/card';
import { MatRippleModule } from '@angular/material/core';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { MatRadioModule } from '@angular/material/radio';
import { TranslateModule } from '@ngx-translate/core';
import { SwuiControlMessagesModule, SwuiTopMenuModule } from '@skywind-group/lib-swui';
import { AddTypeConfirmComponent } from './add-type-confirm/add-type-confirm.component';
import { AddTypeSelectComponent } from './add-type-select/add-type-select.component';
import { ChangeTypeComponent } from './change-type/change-type.component';
import { TwoFactorSettingsComponent } from './two-factor-settings.component';
import { TwoFactorSettingsRouting } from './two-factor-settings.routing';

@NgModule({
  imports: [
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    TranslateModule,
    TwoFactorSettingsRouting,
    MatCardModule,
    SwuiTopMenuModule,
    MatButtonModule,
    MatRadioModule,
    MatFormFieldModule,
    MatInputModule,
    MatIconModule,
    MatRippleModule,
    SwuiControlMessagesModule
  ],
  declarations: [
    TwoFactorSettingsComponent,
    AddTypeSelectComponent,
    AddTypeConfirmComponent,
    ChangeTypeComponent,
  ]
})
export class TwoFactorSettingsModule {
}
