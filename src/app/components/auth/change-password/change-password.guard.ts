import { Injectable } from '@angular/core';
import { CanActivate, Router } from '@angular/router';
import { PERMISSIONS_NAMES, SwHubAuthService } from '@skywind-group/lib-swui';
import { AuthService } from '../../../services/auth/auth.service';

export const MASTER_ID = '1';

@Injectable()
export class ChangePasswordGuard implements CanActivate {

  constructor( private readonly swHubAuthService: SwHubAuthService,
               private readonly router: Router,
               private readonly auth: AuthService,
  ) {
  }

  canActivate() {
    return this.auth.passwordChangeRequired() ||
    (this.swHubAuthService.entityKey !== MASTER_ID &&
      this.swHubAuthService.allowedTo([PERMISSIONS_NAMES.KEYENTITY_USER_CHANGE_PASSWORD])) ?
      true :
      this.router.parseUrl('/pages/404');
  }
}
