import { Compo<PERSON>, <PERSON><PERSON><PERSON>roy, OnInit } from '@angular/core';
import { FormBuilder, FormControl, FormGroup, Validators } from '@angular/forms';
import { Router } from '@angular/router';
import { Subject } from 'rxjs';
import { takeUntil } from 'rxjs/operators';

import { AuthService } from '../../../services/auth/auth.service';
import { ValidationService } from '../../../services/validation.service';


@Component({
  selector: 'sw-change-password',
  styleUrls: ['change-password.component.scss'],
  templateUrl: './change-password.component.html',
})
export class ChangePasswordComponent implements OnInit, OnDestroy {

  form: FormGroup;
  passwordConditionsMin = 1;

  private readonly destroyed = new Subject<void>();

  constructor( private auth: AuthService,
               private router: Router,
               private fb: FormBuilder ) {
    this.form = this.initForm();
  }

  ngOnInit(): void {
    this.newPasswordControl.valueChanges
      .pipe(takeUntil(this.destroyed))
      .subscribe(() => this.confirmPasswordControl.updateValueAndValidity());
  }

  ngOnDestroy(): void {
    this.destroyed.next();
    this.destroyed.complete();
  }

  getIcon(error: string): string {
    return this.newPasswordControl.hasError(error) ? 'remove' : 'check';
  }

  get currentPasswordControl(): FormControl {
    return this.form.get('password') as FormControl;
  }

  get confirmPasswordControl(): FormControl {
    return this.form.get('confirmPassword') as FormControl;
  }

  get newPasswordControl(): FormControl {
    return this.form.get('newPassword') as FormControl;
  }

  onSubmit(event: Event) {
    event.preventDefault();

    if (this.form.valid) {
      this.auth.changePassword(this.form.value)
        .pipe(takeUntil(this.destroyed))
        .subscribe();
    }
  }

  toLogin() {
    this.router.navigate(['auth']);
  }

  private initForm(): FormGroup {
    return this.fb.group({
      password: [
        '', Validators.compose([
          Validators.required,
        ])
      ],
      newPassword: ['', Validators.compose([
        ValidationService.passwordConditions(this.passwordConditionsMin),
        Validators.required,
      ])],
      confirmPassword: ['', Validators.compose([
        ValidationService.passwordEqual('newPassword'),
        Validators.required,
      ])],
    });
  }
}
