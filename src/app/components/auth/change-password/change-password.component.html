<div class="change">
  <mat-card class="change__dialog">
    <mat-card-header class="change__header">
      <mat-card-title>{{'PASSWORD_CHANGE.passwordChange' | translate}}</mat-card-title>
    </mat-card-header>
    <mat-card-content class="change__content" fxLayout="column" fxLayout.gt-md="row">
      <form [formGroup]="form" class="change__form" fxLayout="column">
        <mat-form-field appearance="outline" class="change__field">
          <mat-label>{{'PASSWORD_CHANGE.passwordCurrent' | translate}}</mat-label>
          <input matInput type="password" [formControl]="currentPasswordControl" autocomplete="current-password">
          <mat-error *ngIf="currentPasswordControl.hasError('required')">
            {{'VALIDATION.required' | translate}}</mat-error>
        </mat-form-field>

        <mat-form-field appearance="outline" class="change__field">
          <mat-label>{{'PASSWORD_CHANGE.passwordNew' | translate}}</mat-label>
          <input matInput type="password" [formControl]="newPasswordControl" autocomplete="new-password">
          <mat-error *ngIf="newPasswordControl.hasError('required')">
            {{'VALIDATION.required' | translate}}</mat-error>
        </mat-form-field>

        <mat-form-field appearance="outline" class="change__field">
          <mat-label>{{'PASSWORD_CHANGE.passwordConfirm' | translate}}</mat-label>
          <input matInput type="password" [formControl]="confirmPasswordControl" autocomplete="new-password">
          <mat-error>
            <lib-swui-control-messages [control]="confirmPasswordControl"
                              [messages]="{'passwordNotEquals': ('PASSWORD_CHANGE.passwordNotMatch' | translate)}">
            </lib-swui-control-messages>
          </mat-error>
        </mat-form-field>
      </form>
      <mat-list class="change__requirements" dense>
        <mat-list-item>
          <mat-icon matListIcon>{{getIcon('passwordMinLength')}}</mat-icon>
          {{'PASSWORD_CHANGE.passwordMinLength' | translate}}
        </mat-list-item>

        <mat-list-item>
          <mat-icon matListIcon>{{getIcon('passwordContainLowercase')}}</mat-icon>
          {{'PASSWORD_CHANGE.passwordContainLowercase' | translate: {value: passwordConditionsMin} }}
        </mat-list-item>

        <mat-list-item>
          <mat-icon matListIcon>{{getIcon('passwordContainUppercase')}}</mat-icon>
          {{'PASSWORD_CHANGE.passwordContainUppercase' | translate: {value: passwordConditionsMin} }}
        </mat-list-item>

        <mat-list-item>
          <mat-icon matListIcon>{{getIcon('passwordContainDigit')}}</mat-icon>
          {{'PASSWORD_CHANGE.passwordContainDigit' | translate: {value: passwordConditionsMin} }}
        </mat-list-item>
      </mat-list>
    </mat-card-content>
    <mat-card-actions class="change__actions">
      <button mat-stroked-button color="primary" (click)="toLogin()" class="mat-button-md">
        {{'FORGOTPASSWORD.to-login' | translate}}
      </button>
      <button
        mat-flat-button
        color="primary"
        class="mat-button-md change__submit"
        [disabled]="!form.valid"
        (click)="onSubmit($event)">
        {{'PASSWORD_CHANGE.passwordChange' | translate}}
      </button>
    </mat-card-actions>
  </mat-card>

</div>

