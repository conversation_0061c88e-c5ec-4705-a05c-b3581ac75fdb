import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { ChangePasswordComponent } from './change-password.component';
import { ChangePasswordGuard } from './change-password.guard';


const routes: Routes = [
  {
    path: '',
    component: ChangePasswordComponent,
    canActivate: [ChangePasswordGuard],
  }
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule],
  providers: [ChangePasswordGuard],
})
export class ChangePasswordRoutingModule {
}
