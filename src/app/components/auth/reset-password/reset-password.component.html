<div class="reset">
  <mat-card class="reset__dialog">
    <mat-card-header class="reset__header">
      <mat-card-title>{{'FORGOTPASSWORD.passwordChange' | translate: {username: username} }}</mat-card-title>
    </mat-card-header>
    <mat-card-content class="reset__content" fxLayout="column" fxLayout.gt-md="row">
      <form [formGroup]="form" class="reset__form" fxLayout="column">
        <mat-form-field appearance="outline" class="reset__field">
          <mat-label>{{'FORGOTPASSWORD.new-password' | translate}}</mat-label>
          <input matInput type="password" [formControl]="passwordControl" (focus)="enableField()"
                 autocomplete="current-password">
          <mat-error *ngIf="passwordControl.hasError('required')">
            {{'VALIDATION.required' | translate}}</mat-error>
        </mat-form-field>

        <mat-form-field appearance="outline" class="reset__field">
          <mat-label>{{'FORGOTPASSWORD.confirm-new-password' | translate}}</mat-label>
          <input matInput type="password" [formControl]="newPasswordControl" autocomplete="new-password">
          <mat-error>
            <lib-swui-control-messages [control]="newPasswordControl"
                                       [messages]="messages">
            </lib-swui-control-messages>
          </mat-error>
        </mat-form-field>
      </form>
      <mat-list class="reset__requirements" dense>
        <mat-list-item class="reset__list-item">
          <mat-icon matListIcon>{{getIcon('passwordMinLength')}}</mat-icon>
          {{'PASSWORD_CHANGE.passwordMinLength' | translate}}
        </mat-list-item>

        <mat-list-item class="reset__list-item">
          <mat-icon matListIcon>{{getIcon('passwordContainLowercase')}}</mat-icon>
          {{'PASSWORD_CHANGE.passwordContainLowercase' | translate: {value: passwordConditionsMin} }}
        </mat-list-item>

        <mat-list-item class="reset__list-item">
          <mat-icon matListIcon>{{getIcon('passwordContainUppercase')}}</mat-icon>
          {{'PASSWORD_CHANGE.passwordContainUppercase' | translate: {value: passwordConditionsMin} }}
        </mat-list-item>

        <mat-list-item class="reset__list-item">
          <mat-icon matListIcon>{{getIcon('passwordContainDigit')}}</mat-icon>
          {{'PASSWORD_CHANGE.passwordContainDigit' | translate: {value: passwordConditionsMin} }}
        </mat-list-item>
      </mat-list>
    </mat-card-content>
    <mat-card-actions class="reset__actions">
      <button mat-stroked-button color="primary" (click)="toLogin()" class="mat-button-md">
        {{'FORGOTPASSWORD.to-login' | translate}}
      </button>
      <button
        mat-flat-button
        color="primary"
        class="mat-button-md reset__submit"
        [disabled]="!form.valid"
        (click)="onSubmit($event)">
        {{'FORGOTPASSWORD.reset' | translate}}
      </button>
    </mat-card-actions>
  </mat-card>
</div>

