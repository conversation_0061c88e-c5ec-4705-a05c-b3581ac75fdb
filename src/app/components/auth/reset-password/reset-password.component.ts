import { ChangeDetectionStrategy, ChangeDetectorRef, Component, On<PERSON><PERSON>roy, OnInit } from '@angular/core';
import { Form<PERSON><PERSON>er, FormControl, FormGroup, Validators } from '@angular/forms';
import { ActivatedRoute, Router } from '@angular/router';
import { TranslateService } from '@ngx-translate/core';
import { SwuiNotificationsService } from '@skywind-group/lib-swui';
import { Subject } from 'rxjs';
import { filter, finalize, take, takeUntil } from 'rxjs/operators';

import { AuthService } from '../../../services/auth/auth.service';
import { ValidationService } from '../../../services/validation.service';


@Component({
  selector: 'sw-reset-password',
  styleUrls: ['reset-password.component.scss'],
  templateUrl: './reset-password.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class ResetPasswordComponent implements OnInit, On<PERSON><PERSON>roy {

  form: FormGroup;
  passwordConditionsMin = 1;

  readonly username: string;
  readonly messages = {
    passwordNotEquals: 'PASSWORD_CHANGE.passwordNotMatch',
    required: 'VALIDATION.required'
  };
  private readonly destroy$ = new Subject<void>();
  private readonly token: string;
  private readonly secretKey: string;

  constructor( private auth: AuthService,
               private fb: FormBuilder,
               private notificationsService: SwuiNotificationsService,
               private router: Router,
               private translate: TranslateService,
               private cdr: ChangeDetectorRef,
               { snapshot: { queryParams: { token, secretKey, username } } }: ActivatedRoute,
  ) {
    this.form = this.initForm();
    this.username = username;
    this.secretKey = secretKey;
    this.token = decodeURIComponent(token).replace(/\s/g, '+');
  }

  ngOnInit() {
    this.passwordControl
      .valueChanges
      .pipe(
        filter(() => this.newPasswordControl.dirty),
        takeUntil(this.destroy$)
      )
      .subscribe(() => {
        this.newPasswordControl.updateValueAndValidity();
        this.cdr.detectChanges();
      });
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  getIcon( error: string ): string {
    return this.newPasswordControl.hasError(error) ? 'remove' : 'check';
  }

  enableField() {
    this.newPasswordControl.enable({ emitEvent: false });
  }

  get passwordControl(): FormControl {
    return this.form.get('password') as FormControl;
  }

  get newPasswordControl(): FormControl {
    return this.form.get('newPassword') as FormControl;
  }

  onSubmit( event: Event ) {
    event.preventDefault();

    if (this.form.valid) {
      const body = {
        secretKey: this.secretKey,
        username: this.username,
        token: this.token,
        newPassword: this.newPasswordControl.value,
      };

      this.auth.changePassword(body)
        .pipe(
          finalize(() => this.router.navigate(['/auth/login'])),
          take(1)
        )
        .subscribe(
          () => {
            this.notificationsService.success(this.translate.instant('FORGOTPASSWORD.notificationReset'), '');
          },
          ( error ) => {
            this.notificationsService.error(error.error.message, '');
          }
        );
    }
  }

  toLogin() {
    this.router.navigate(['auth']);
  }

  private initForm(): FormGroup {
    return this.fb.group({
      password: [
        '', Validators.compose([
          Validators.required,
        ])
      ],
      newPassword: [
        { value: '', disabled: true }, Validators.compose([
          ValidationService.passwordConditions(this.passwordConditionsMin),
          ValidationService.passwordEqual('password'),
          Validators.required,
        ])
      ]
    });
  }
}
