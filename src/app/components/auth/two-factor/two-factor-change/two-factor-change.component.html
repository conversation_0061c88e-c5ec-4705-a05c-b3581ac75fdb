<ng-container *ngIf="!selected">
  <div class="factor__message">{{ 'TWOFA.subTitle_pleaseSelectChange' | translate }}</div>
  <mat-radio-group class="factor__radio">
    <mat-radio-button *ngFor="let type of userAuthTypes" [value]="type.name" [checked]="authType?.name === type.name"
                      (click)="changeAuthType(type)">
      {{ type.displayName | translate }}
    </mat-radio-button>
  </mat-radio-group>

  <div class="factor__actions">
    <button mat-stroked-button (click)="submitChangedType($event)">
      {{ 'TWOFA.next' | translate }}
      <mat-icon>arrow_forward</mat-icon>
    </button>
  </div>
</ng-container>
