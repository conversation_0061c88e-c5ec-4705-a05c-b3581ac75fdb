import { Component, EventEmitter, OnDestroy, OnInit, Output } from '@angular/core';
import { Subscription } from 'rxjs';

import { TwoFactorManagerService } from '../two-factor-manager.service';
import { TwofaTypeItem } from '../two-factor.model';
import { AuthService } from '../../../../services/auth/auth.service';
import { IDexieTwoFactorContactInfo } from '../../../../models/dexie.model';


@Component({
  selector: 'sw-two-factor-change',
  styleUrls: ['./../two-factor.component.scss'],
  templateUrl: './two-factor-change.component.html'
})

export class TwoFactorChangeComponent implements OnInit, OnDestroy {

  contactInfo: IDexieTwoFactorContactInfo;
  authType: TwofaTypeItem;
  userAuthTypes: TwofaTypeItem[];
  selected: boolean;

  @Output() typeChange: EventEmitter<string> = new EventEmitter();

  private subscriptions: Subscription[] = [];

  constructor(
    private manager: TwoFactorManagerService,
    private auth: AuthService,
  ) {
  }

  ngOnInit() {
    this.subscribeToData();
  }

  ngOnDestroy() {
    this.subscriptions.forEach(sub => sub.unsubscribe());
  }

  submitChangedType() {
    if (this.authType.challengeOnChange) {
      this.performChallenge();
    } else {
      this.completeStep();
    }
  }

  changeAuthType(type: TwofaTypeItem) {
    this.manager.setAuthType(type.name);
  }

  private subscribeToData() {
    const typeSub = this.manager.authType$.subscribe(type => this.authType = type);
    const typesSub = this.manager.userAuthTypes$.subscribe(types => this.userAuthTypes = types);
    const infoSub = this.manager.contactInfo$.subscribe(info => this.contactInfo = info);
    this.subscriptions.push(typeSub, typesSub, infoSub);
  }

  private performChallenge() {
    this.auth.twofaChallenge({authType: this.authType.name, contactInfo: ''}).subscribe(
      () => {
        this.completeStep();
      }
    );
  }

  private completeStep() {
    this.typeChange.emit(this.authType.name);
  }

}
