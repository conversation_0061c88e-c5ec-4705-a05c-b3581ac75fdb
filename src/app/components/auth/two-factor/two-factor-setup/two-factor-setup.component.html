<ng-container *ngIf="!selected || !submitted">
  <div class="factor__message">{{'TWOFA.subTitle_pleaseSelect' | translate}}</div>
  <mat-radio-group class="factor__radio" [formControl]="radioControl">
    <mat-radio-button *ngFor="let type of types" [value]="type">
      {{ type.displayName | translate }}
    </mat-radio-button>
  </mat-radio-group>

  <div class="factor__actions">
    <button mat-stroked-button color="primary" (click)="toLogin()" class="mat-button-md">
      {{'FORGOTPASSWORD.to-login' | translate}}
    </button>
    <button
      mat-stroked-button
      color="primary"
      class="mat-button-md factor__submit"
      (click)="submitCheckedType($event)"
      [disabled]="!selected">
      {{ 'TWOFA.next' | translate }}
      <mat-icon>arrow_forward</mat-icon>
    </button>
  </div>
</ng-container>

<ng-container *ngIf="isGoogle && submitted">
  <div class="factor__message">{{ 'TWOFA.googleAuth_scanThisQRCode' | translate }}</div>
  <img class="factor__image" [src]="base64QRCodeURI" alt/>
  <div *ngIf="stringSecretKey" class="factor__secret">
    <a mat-ripple class="factor__link factor__key-toggle" href (click)="!(showGoogleAuthKey = true)" *ngIf="!showGoogleAuthKey">
      {{ 'TWOFA.googleAuth_showStringSecretKey' | translate }}
    </a>
    <div class="factor__key" *ngIf="showGoogleAuthKey">{{ stringSecretKey }}</div>
  </div>

  <div class="factor__actions">
    <button
      mat-stroked-button
      color="primary"
      class="mat-button-md factor__grow" (click)="backToList($event)">
      <mat-icon>arrow_back</mat-icon>
      {{'SETTINGS.back' | translate}}
    </button>
    <button
      mat-stroked-button
      color="primary"
      class="mat-button-md factor__grow"
      (click)="confirmSelectedType($event)">
      {{ 'TWOFA.next' | translate }}
      <mat-icon>arrow_forward</mat-icon>
    </button>
  </div>
</ng-container>

<ng-container *ngIf="isLoading">
  <div class="factor__spinner">
      <mat-spinner color="primary" [diameter]="48"></mat-spinner>
  </div>
</ng-container>

<ng-container *ngIf="isSMS && submitted">
  <div class="factor__message">{{ 'TWOFA.sms_pleaseEnterPhoneNumber' | translate }}</div>

  <form [formGroup]="phoneNumberForm.get('contactInfo')">
    <mat-form-field appearance="outline" class="factor__field factor__field--phone">
      <mat-label>{{ 'TWOFA.phoneNumber' | translate }}</mat-label>
      <mat-icon matPrefix style="margin-right: 6px">phone</mat-icon>
      <input matInput type="text" formControlName="sms" id="phoneNumber">
       <mat-error>
         <lib-swui-control-messages [control]="phoneNumberForm.get('contactInfo.sms')" [messages]="errorMessages">
         </lib-swui-control-messages>
       </mat-error>
    </mat-form-field>
  </form>
  <div class="factor__actions">
    <button
      mat-stroked-button
      color="primary"
      class="mat-button-md factor__grow" (click)="backToList($event)">
      <mat-icon>arrow_back</mat-icon>
      {{'SETTINGS.back' | translate}}
    </button>
    <button
      mat-stroked-button
      color="primary"
      class="mat-button-md factor__grow"
      (click)="confirmSelectedType($event)"
      [disabled]="!phoneNumberForm.valid">
      {{ 'TWOFA.next' | translate }}
      <mat-icon>arrow_forward</mat-icon>
    </button>
  </div>
</ng-container>
