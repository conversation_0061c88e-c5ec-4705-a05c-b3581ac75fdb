import { Component, EventEmitter, Input, OnDestroy, OnInit, Output } from '@angular/core';
import { Form<PERSON><PERSON>er, FormControl, FormGroup, Validators } from '@angular/forms';
import { Router } from '@angular/router';
import { Subject } from 'rxjs';
import { finalize, takeUntil } from 'rxjs/operators';

import { AuthService } from '../../../../services/auth/auth.service';
import { ValidationService } from '../../../../services/validation.service';
import { getTwofaType, isEmail, isGoogle, isSMS, TwofaTypeItem } from '../two-factor.model';


@Component({
  selector: 'sw-two-factor-setup',
  styleUrls: ['./../two-factor.component.scss'],
  templateUrl: './two-factor-setup.component.html',
})
export class TwoFactorSetupComponent implements OnInit, OnDestroy {
  errorMessages: { [key: string]: any } = {
    required: 'VALIDATION.required',
    invalidPhoneNumberMask: 'TWOFA.sms_invalidNumber'
  };

  types: TwofaTypeItem[];
  selected: TwofaTypeItem;
  phoneNumberForm: FormGroup;
  submitted = false;
  base64QRCodeURI = 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAANQAAADUCAYAAADk3g0YAAAAAklEQVR4AewaftIAAAqDSURBVO3BQY7';
  stringSecretKey = '';
  showGoogleAuthKey = false;

  isGoogle = false;
  isSMS = false;
  isEmail = false;
  isLoading = false;

  radioControl = new FormControl();

  @Input() changing = false;
  @Output() setupComplete: EventEmitter<any> = new EventEmitter();
  private readonly destroyed = new Subject<void>();

  constructor( private auth: AuthService,
               private fb: FormBuilder,
               private router: Router
  ) {
    if (!this.auth.twoFactorSetupRequired()) {
      this.router.navigate(['auth/login']);
    }
  }

  ngOnInit() {
    this.setAvailableTypes();

    this.radioControl.valueChanges
      .pipe(takeUntil(this.destroyed))
      .subscribe(( val: TwofaTypeItem ) => {
        this.selected = val;
        this.isEmail = isEmail(val);
        this.isSMS = isSMS(val);
        this.isGoogle = isGoogle(val);
      });

    this.initForms();
    this.checkForOnlyOneTypeAvailable();
  }

  ngOnDestroy(): void {
    this.destroyed.next();
    this.destroyed.complete();
  }

  submitCheckedType( event? ) {
    event?.preventDefault();
    this.submitted = true;
    this.useCheckedType();
  }

  confirmSelectedType( event: Event ) {
    event.preventDefault();

    this.submitted = true;
    this.isSMS && this.auth.phoneNumberRequired() ? this.saveSmsType() : this.completeSetup();
  }

  toLogin() {
    this.router.navigate(['auth']);
  }

  backToList(event: MouseEvent) {
    event.preventDefault();
    this.radioControl.reset();
    this.submitted = false;
  }

  private useCheckedType() {
    const canSaveToBackend = !this.isSMS || (this.isSMS && !this.auth.phoneNumberRequired());
    canSaveToBackend ? this.saveSelectedType() : this.setSelectedType();
  }

  private initForms() {
    this.phoneNumberForm = this.fb.group({
      contactInfo: this.fb.group({
        sms: [
          '', Validators.compose([
            Validators.required,
            ValidationService.phoneNumberValidator,
          ])
        ]
      })
    });
  }

  /**
   * Set checked type as selected in component only
   */
  private setSelectedType() {
    this.auth.twoFactorManagerSetAuthType(this.selected.name);
    const isSetupComplete = this.isEmail || (this.isSMS && !this.auth.phoneNumberRequired());
    if (isSetupComplete) {
      this.completeSetup();
    }
  }

  private checkForOnlyOneTypeAvailable() {
    if (this.types.length === 1) {
      this.radioControl.setValue(this.types[0]);
      this.submitCheckedType();
    }
  }

  private completeSetup() {
    this.setupComplete.emit();
  }

  /**
   * Saving checked type as selected to backend
   */
  private saveSelectedType() {
    this.isLoading = true;
    const data = { authType: this.selected.name, contactInfo: '' };
    this.auth.twofaChallenge(data)
      .pipe(
        finalize(() => this.isLoading = false),
        takeUntil(this.destroyed)
      )
      .subscribe(( response ) => {
          this.grabGoogleAuthParams(response);
          this.setSelectedType();
        }
      );
  }

  /**
   * Saving sms type as selected to backend if user is required to enter phone number while setup
   */
  private saveSmsType() {
    if (this.phoneNumberForm.valid) {
      this.isLoading = true;
      const data = {
        authType: this.selected.name,
        contactInfo: this.phoneNumberForm.get('contactInfo.sms').value
      };

      this.auth.twofaChallenge(data)
        .pipe(
          finalize(() => this.isLoading = false),
          takeUntil(this.destroyed)
        )
        .subscribe(() => {
          this.auth.twoFactorManagerSetContactInfo(this.phoneNumberForm.get('contactInfo').value);
          this.completeSetup();
        });
    }
  }

  private grabGoogleAuthParams( response ) {
    if (this.isGoogle) {
      this.base64QRCodeURI = response['totpUri'];
      if ('gaSecretKey' in response) {
        this.stringSecretKey = response['gaSecretKey'];
      }
    }
  }

  private setAvailableTypes() {
    const types = this.auth.twoFactorManagerGetBrandTypes();
    this.types = types.map(name => getTwofaType(name));
  }
}
