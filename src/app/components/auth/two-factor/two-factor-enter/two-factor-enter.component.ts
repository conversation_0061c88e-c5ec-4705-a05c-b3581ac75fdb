import { Component, Input, On<PERSON><PERSON>roy, OnInit } from '@angular/core';
import { Subscription } from 'rxjs';

import { TWOFA_TYPE_NAMES, TwofaTypeItem } from '../two-factor.model';
import { TwoFactorManagerService } from '../two-factor-manager.service';
import { IDexieTwoFactorContactInfo } from '../../../../models/dexie.model';


@Component({
  selector: 'sw-two-factor-enter',
  styleUrls: ['./../two-factor.component.scss'],
  templateUrl: './two-factor-enter.component.html'
})

export class TwoFactorEnterComponent implements OnInit, OnDestroy {

  @Input() change: boolean;

  contactInfo: IDexieTwoFactorContactInfo;
  userAuthTypes: TwofaTypeItem[];
  authType: TwofaTypeItem;
  twofaTypes = TWOFA_TYPE_NAMES;

  private subscriptions: Subscription[] = [];

  constructor( private manager: TwoFactorManagerService ) {
  }


  ngOnInit() {
    this.subscribeToData();
  }

  ngOnDestroy() {
    this.subscriptions.forEach(sub => sub.unsubscribe());
  }

  submitAuthCode(data) {
    this.manager.submitAuthCode(data);
  }

  isAllowedToChangeType(): boolean {
    return this.userAuthTypes && this.userAuthTypes.length > 1;
  }

  private subscribeToData() {
    const userTypesSub = this.manager.userAuthTypes$.subscribe(types => this.userAuthTypes = types);
    const authTypeSub = this.manager.authType$.subscribe(type => {
      this.authType = type;
    });
    const infoSub = this.manager.contactInfo$.subscribe(info => this.contactInfo = info);
    this.subscriptions.push(userTypesSub, authTypeSub, infoSub);
  }
}
