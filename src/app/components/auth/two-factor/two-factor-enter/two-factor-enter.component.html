<div class="factor__subtitle">{{ 'TWOFA.subTitle_confirmation' | translate }}</div>

<div class="factor__message" [ngSwitch]="authType.name">
  <ng-container *ngSwitchCase="twofaTypes.sms">
    {{ 'TWOFA.sms_pleaseEnterCode' | translate: { number: contactInfo?.sms } }}
  </ng-container>
  <ng-container *ngSwitchCase="twofaTypes.email">
    {{ 'TWOFA.email_pleaseEnterCode' | translate: { email: contactInfo?.email } }}
  </ng-container>
  <ng-container *ngSwitchCase="twofaTypes.google">
    {{ 'TWOFA.googleAuth_pleaseEnterCode' | translate: { email: contactInfo?.email } }}
  </ng-container>
</div>

<div class="factor__secret" *ngIf="isAllowedToChangeType()">
  <router-link-wrapper [translateConstant]="'TWOFA.youCanChangeType'"
                       [routerLinkData]="['/auth/login/two-factor/change']">
  </router-link-wrapper>
</div>

<sw-two-factor-code-form (formSubmitted)="submitAuthCode($event)"></sw-two-factor-code-form>
