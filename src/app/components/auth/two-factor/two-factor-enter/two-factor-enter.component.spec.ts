import { ComponentFixture, TestBed, waitForAsync } from '@angular/core/testing';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { NO_ERRORS_SCHEMA } from '@angular/core';
import { TranslateModule } from '@ngx-translate/core';
import { TwoFactorEnterComponent } from './two-factor-enter.component';
import { TwoFactorManagerService } from '../two-factor-manager.service';
import { getTwofaType, TWOFA_TYPE_NAMES, TwofaTypeItem } from '../two-factor.model';

import { AuthService } from '../../../../services/auth/auth.service';
import { HttpClientTestingModule } from '@angular/common/http/testing';
import { RouterTestingModule } from '@angular/router/testing';
import { RedirectService } from '../../../../services/redirect.service';
import { IDexieTwoFactorConfig } from '../../../../models/dexie.model';
import { SwuiNotificationsService } from '@skywind-group/lib-swui';
import createSpyObj = jasmine.createSpyObj;

describe('TwoFactorEnterComponent', () => {
  let fixture: ComponentFixture<TwoFactorEnterComponent>;
  let component: TwoFactorEnterComponent;

  let managerService: TwoFactorManagerService;
  let twoFactorConfigMock: IDexieTwoFactorConfig = {
    token: '2fa-auth-token-goes-here...',
    defaultAuthType: 'sms',
    brandAuthTypes: ['google', 'sms', 'email'],
    userAuthTypes: ['google', 'sms', 'email'],
    contactInfo: {
      sms: '+999888777666555',
      email: '<EMAIL>'
    }
  };

  const userAuthTypesMock: TwofaTypeItem[] = Object.keys(TWOFA_TYPE_NAMES).map(item => getTwofaType(item));

  const codeMock = {
    valid: {
      authCode: '013370'
    }
  };

  beforeEach(async () => {
    const authServiceMock = createSpyObj('AuthService', [
      'twoFactorManagerGetConfig',
      'twoFactorManagerSetAuthType'
    ]);
    const redirectServiceMock = createSpyObj('RedirectService', ['navigate']);
    authServiceMock.twoFactorManagerGetConfig.and.returnValue(twoFactorConfigMock);

    TestBed.configureTestingModule({
      declarations: [TwoFactorEnterComponent],
      providers: [
        TwoFactorManagerService,
        { provide: RedirectService, useValue: redirectServiceMock },
        { provide: AuthService, useValue: authServiceMock },
        { provide: SwuiNotificationsService, useValue: {} },
      ],
      schemas: [
        NO_ERRORS_SCHEMA,
      ],
      imports: [
        FormsModule,
        ReactiveFormsModule,
        TranslateModule.forRoot(),
        HttpClientTestingModule,
        RouterTestingModule,
      ],
    }).compileComponents();
  });

  beforeEach(() => {
    managerService = TestBed.inject(TwoFactorManagerService);
    fixture = TestBed.createComponent(TwoFactorEnterComponent);
    component = fixture.componentInstance;

    component.ngOnInit();
    managerService.fetchAuthDetails();
    managerService.setAuthType('sms');
    fixture.detectChanges();
  });

  afterEach(() => {

    component.ngOnDestroy();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  describe('subscribeToData', () => {
    it('should set user auth types on subscribe', waitForAsync(() => {
      fixture.whenStable().then(() => {
        expect(component.userAuthTypes[0]).toEqual(userAuthTypesMock[0]);
      });
    }));

    it('should set active auth type on subscribe', waitForAsync(() => {
      fixture.whenStable().then(() => {
        expect(component.authType).toBeTruthy();
      });
    }));
  });

  it('should allow/disabled to change 2fa type for one/many auth types', () => {
    component.userAuthTypes = userAuthTypesMock;
    expect(component.isAllowedToChangeType()).toBe(true);

    component.userAuthTypes = [userAuthTypesMock[0]];
    expect(component.isAllowedToChangeType()).toBe(false);
  });

  it('should send auth data to manager', waitForAsync(() => {
    const submitSpy = spyOn(managerService, 'submitAuthCode');
    component.submitAuthCode(codeMock.valid);

    fixture.whenStable().then(() => {
      expect(submitSpy).toHaveBeenCalledWith(codeMock.valid);
    });
  }));

});
