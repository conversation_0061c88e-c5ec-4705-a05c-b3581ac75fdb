import { Component } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';

import { TwoFactorManagerService } from './two-factor-manager.service';
import { AuthService } from '../../../services/auth/auth.service';
import { SwHubConfigService } from '@skywind-group/lib-swui';

export const STEPS = {
  'enter': 'enter',
  'setup': 'setup',
  'confirm': 'confirm',
  'change': 'change',
};

@Component({
  selector: 'sw-hub-base-two-factor',
  templateUrl: './two-factor.component.html',
  styleUrls: ['./two-factor.component.scss']
})
export class TwoFactorComponent {
  readonly logo: string;

  stepNames = STEPS;
  steps: string[] = [STEPS.enter, STEPS.setup, STEPS.confirm, STEPS.change];
  step: string;

  constructor( private auth: AuthService,
               private manager: TwoFactorManagerService,
               private router: Router,
               private route: ActivatedRoute,
               { logo }: SwHubConfigService,
  ) {
    this.logo = logo?.solo ?? 'img/logo-skywind-solo.png';
    this.setStep();

    if (!this.auth.twoFactorRequired() && !this.auth.twoFactorSetupRequired()) {
      this.router.navigate(['auth/login']);
    } else {
      this.manager.fetchAuthDetails();
    }
  }

  setStep() {
    this.step = this.route.snapshot.params['step'] ? this.route.snapshot.params['step'] : STEPS.enter;

    if (this.steps.indexOf(this.step) === -1) {
      this.router.navigate(['auth/login/two-factor']);
    }
  }

  onSetupStepComplete() {
    this.step = STEPS.confirm;
    this.manager.fetchAuthDetails();
    this.router.navigate(['auth/login/two-factor/confirm']);
  }

  onChangeStepComplete() {
    this.step = STEPS.enter;
    this.manager.fetchAuthDetails();
    this.router.navigate(['auth/login/two-factor']);
  }


}
