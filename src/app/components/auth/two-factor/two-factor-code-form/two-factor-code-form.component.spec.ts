import { NO_ERRORS_SCHEMA } from '@angular/core';
import { ComponentFixture, TestBed } from '@angular/core/testing';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { TranslateModule } from '@ngx-translate/core';

import { TwoFactorCodeFormComponent } from './two-factor-code-form.component';


describe('CodeFormComponent', () => {

  let component: TwoFactorCodeFormComponent;
  let fixture: ComponentFixture<TwoFactorCodeFormComponent>;

  beforeEach(async () => {

    TestBed.configureTestingModule({
      declarations: [TwoFactorCodeFormComponent],
      providers: [],
      schemas: [
        NO_ERRORS_SCHEMA,
      ],
      imports: [
        FormsModule,
        ReactiveFormsModule,
        TranslateModule.forRoot(),
      ],
    }).compileComponents();
  });

  beforeEach(() => {
    fixture = TestBed.createComponent(TwoFactorCodeFormComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
