<mat-error *ngIf="errorMsg">{{ errorMsg }}</mat-error>

<mat-form-field appearance="outline" class="factor__field">
  <mat-label>{{ 'TWOFA.code' | translate }}</mat-label>
  <mat-icon matPrefix style="margin-right: 6px">security</mat-icon>
  <input matInput type="text" [formControl]="codeControl">
  <mat-error>
    <lib-swui-control-messages [control]="codeControl" [force]="submitted" [messages]="errorMessages">
    </lib-swui-control-messages>
  </mat-error>
</mat-form-field>
<div class="actions">
  <a mat-stroked-button color="primary" [routerLink]="['/auth/managekeys']" class="mat-button-md">
    {{ 'LOGIN.keysManagement' | translate }}
  </a>
  <button
    mat-flat-button
    color="primary"
    class="mat-button-md actions__submit"
    (click)="submitForm()"
    [disabled]="codeControl.invalid">
    {{ 'TWOFA.submit' | translate }}
  </button>
</div>

