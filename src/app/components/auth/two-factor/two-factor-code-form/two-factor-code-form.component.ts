import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { FormControl, Validators } from '@angular/forms';

import { ValidationService } from '../../../../services/validation.service';


@Component({
  selector: 'sw-two-factor-code-form',
  styleUrls: ['./../two-factor.component.scss'],
  templateUrl: './two-factor-code-form.component.html',
})
export class TwoFactorCodeFormComponent implements OnInit {
  @Input() errorMsg: string;
  @Output() formSubmitted: EventEmitter<Object> = new EventEmitter();

  submitted = false;
  codeControl: FormControl = new FormControl('');
  errorMessages: {[key: string]: any} = {
    required: 'VALIDATION.required',
    invalidDigitsOnly: 'TWOFA.validationError_codeInvalid'
  };

  constructor() {
  }

  ngOnInit(): void {
    this.codeControl.setValidators(Validators.compose([
      Validators.required,
      ValidationService.digitsOnlyValidator
    ]));
  }

  submitForm() {
    this.submitted = true;
    if (this.codeControl.valid) {
      this.formSubmitted.emit(this.codeControl.value);
    }
  }
}
