<div class="factor">
  <div class="factor__wrapper">
    <mat-card class="factor__dialog mat-elevation-z0">
      <div class="factor__logo">
        <img [src]="logo" alt="{{ 'skywindFalconLogoAlt' | translate }}">
      </div>
      <mat-card-header>
        <mat-card-title class="factor__title">{{ 'TWOFA.title' | translate }}</mat-card-title>
      </mat-card-header>
      <mat-card-content [ngSwitch]="step" class="factor__content">

        <sw-two-factor-enter *ngSwitchCase="stepNames.enter">
        </sw-two-factor-enter>

        <sw-two-factor-setup *ngSwitchCase="stepNames.setup" (setupComplete)="onSetupStepComplete($event)">
        </sw-two-factor-setup>

        <sw-two-factor-confirm *ngSwitchCase="stepNames.confirm">
        </sw-two-factor-confirm>

        <sw-two-factor-change *ngSwitchCase="stepNames.change" (typeChange)="onChangeStepComplete($event)">
        </sw-two-factor-change>

      </mat-card-content>
    </mat-card>
  </div>
</div>

