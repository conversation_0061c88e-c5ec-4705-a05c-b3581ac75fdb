<div class="factor__subtitle">{{ 'TWOFA.subTitle_confirmation' | translate }}</div>

<div class="factor__message" [ngSwitch]="authType.name">
  <span *ngSwitchCase="twofaTypes.sms">
    {{ 'TWOFA.sms_pleaseEnterCode' | translate: { number: contactInfo?.sms } }}
  </span>
  <span *ngSwitchCase="twofaTypes.email">
    {{ 'TWOFA.email_pleaseEnterCode' | translate: { email: contactInfo?.email } }}
  </span>
  <span *ngSwitchCase="twofaTypes.google">
    {{ 'TWOFA.googleAuth_pleaseEnterCode' | translate: { email: contactInfo?.email } }}
  </span>
  <span>{{ 'TWOFA.setupConfirm_postfix' | translate }}</span>
</div>

<sw-two-factor-code-form (formSubmitted)="submitConfirmCode($event)"></sw-two-factor-code-form>
