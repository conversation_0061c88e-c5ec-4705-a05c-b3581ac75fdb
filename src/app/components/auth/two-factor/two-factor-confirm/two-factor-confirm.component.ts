import { Component, <PERSON><PERSON><PERSON>roy, OnInit } from '@angular/core';
import { Subscription } from 'rxjs';

import { TWOFA_TYPE_NAMES, TwofaTypeItem } from '../two-factor.model';
import { TwoFactorManagerService } from '../two-factor-manager.service';
import { IDexieTwoFactorContactInfo } from '../../../../models/dexie.model';


@Component({
  selector: 'sw-two-factor-confirm',
  styleUrls: ['./../two-factor.component.scss'],
  templateUrl: './two-factor-confirm.component.html'
})

export class TwoFactorConfirmComponent implements OnInit, OnDestroy {

  contactInfo: IDexieTwoFactorContactInfo;
  authType: TwofaTypeItem;
  twofaTypes = TWOFA_TYPE_NAMES;

  private subscriptions: Subscription[] = [];

  constructor(
    private manager: TwoFactorManagerService,
  ) {
    this.subscribeToData();
  }

  ngOnInit() {
    this.manager.fetchAuthDetails();
  }

  ngOnDestroy() {
    this.subscriptions.forEach(sub => sub.unsubscribe());
  }

  submitConfirmCode( data ) {
    this.manager.submitConfirmCode(data);
  }

  private subscribeToData() {
    const typeSub = this.manager.authType$.subscribe(type => this.authType = type);
    const infoSub = this.manager.contactInfo$.subscribe(info => this.contactInfo = info);
    this.subscriptions.push(typeSub, infoSub);
  }
}
