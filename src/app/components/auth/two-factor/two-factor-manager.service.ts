import { Injectable } from '@angular/core';
import { ReplaySubject } from 'rxjs';

import { AuthService } from '../../../services/auth/auth.service';
import { getTwofaType, isSMS, TwofaTypeItem } from './two-factor.model';
import { LoginInfo } from '../../../models/user.model';
import { IDexieTwoFactorConfig, IDexieTwoFactorContactInfo } from '../../../models/dexie.model';


@Injectable()
export class TwoFactorManagerService {

  authType$: ReplaySubject<TwofaTypeItem> = new ReplaySubject(1);
  userAuthTypes$: ReplaySubject<TwofaTypeItem[]> = new ReplaySubject(1);
  contactInfo$: ReplaySubject<IDexieTwoFactorContactInfo> = new ReplaySubject(1);

  private authType: TwofaTypeItem;
  private contactInfo: IDexieTwoFactorContactInfo;

  constructor(
    private auth: AuthService,
  ) {
    this.authType$.subscribe(type => this.authType = type);
    this.contactInfo$.subscribe(info => this.contactInfo = info);
  }

  submitAuthCode( authCode ) {
    const authType = this.authType.name;
    this.auth.submitTwoFACode(authCode, authType).subscribe(
      ( response ) => this.authorize(response)
    );
  }

  submitConfirmCode( authCode ) {
    let contactInfo;

    if (isSMS(this.authType) && this.auth.phoneNumberRequired()) {
      // because full phone number as contactInfo is required by backend
      contactInfo = this.contactInfo.sms;
    }

    this.auth.confirmTwoFASetup({ authCode, contactInfo }).subscribe(
      ( response ) => this.authorize(response)
    );
  }

  fetchAuthDetails() {
    const config = this.auth.twoFactorManagerGetConfig() as IDexieTwoFactorConfig;

    if ('defaultAuthType' in config) {
      this.setAuthType(config.defaultAuthType);
    }
    if ('contactInfo' in config) {
      this.contactInfo$.next(config.contactInfo);
    }
    if ('userAuthTypes' in config) {
      this.userAuthTypes$.next(config.userAuthTypes.map(type => getTwofaType(type)));
    }
  }

  setAuthType( authType: string ) {
    this.authType$.next(getTwofaType(authType));
    this.auth.twoFactorManagerSetAuthType(authType);
  }

  private authorize( response ) {
    this.auth.authorize({ ...response, twoFactor: true } as LoginInfo);
  }

}
