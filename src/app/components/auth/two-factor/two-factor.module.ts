import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { TranslateModule } from '@ngx-translate/core';
import { SwuiControlMessagesModule } from '@skywind-group/lib-swui';
import { RouterLinkWrapperModule } from '../../../common/components/router-link-wrapper/router-link-wrapper.module';
import { ValidationService } from '../../../services/validation.service';
import { TwoFactorRoutingModule } from './two-factor.routing';
import { TwoFactorComponent } from './two-factor.component';
import { TwoFactorSetupComponent } from './two-factor-setup/two-factor-setup.component';
import { TwoFactorEnterComponent } from './two-factor-enter/two-factor-enter.component';
import { TwoFactorConfirmComponent } from './two-factor-confirm/two-factor-confirm.component';
import { TwoFactorChangeComponent } from './two-factor-change/two-factor-change.component';
import { TwoFactorCodeFormComponent } from './two-factor-code-form/two-factor-code-form.component';
import { TwoFactorManagerService } from './two-factor-manager.service';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIconModule } from '@angular/material/icon';
import { MatCardModule } from '@angular/material/card';
import { MatInputModule } from '@angular/material/input';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatButtonModule } from '@angular/material/button';
import { MatRadioModule } from '@angular/material/radio';
import { MatRippleModule } from '@angular/material/core';


export const twoFactorMatModules = [
  MatButtonModule,
  MatInputModule,
  MatIconModule,
  MatFormFieldModule,
  MatCardModule,
  MatRippleModule,
  MatRadioModule,
  MatProgressSpinnerModule,
];

@NgModule({
  imports: [
    CommonModule,
    ReactiveFormsModule,
    FormsModule,
    TranslateModule,
    TwoFactorRoutingModule,
    RouterLinkWrapperModule,
    SwuiControlMessagesModule,
    ...twoFactorMatModules,
  ],
  declarations: [
    TwoFactorComponent,
    TwoFactorSetupComponent,
    TwoFactorEnterComponent,
    TwoFactorConfirmComponent,
    TwoFactorChangeComponent,
    TwoFactorCodeFormComponent,
  ],
  providers: [
    ValidationService,
    TwoFactorManagerService,
  ]

})
export class TwoFactorModule {
}
