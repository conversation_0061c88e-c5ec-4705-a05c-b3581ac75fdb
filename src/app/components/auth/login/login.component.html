<div class="login">
  <mat-card class="login__dialog dialog">
    <form [formGroup]="form" class="dialog__form">

      <div class="dialog__header">
        <div class="dialog__logo">
          <img src="{{ logo }}" alt="{{ 'skywindFalconLogoAlt' | translate }}">
        </div>
        <h2 class="mat-title dialog__title">{{ 'LOGIN.title' | translate }}</h2>
      </div>

      <div class="dialog__body">
        <mat-form-field class="dialog__field" appearance="outline">
          <mat-label>{{ 'LOGIN.username' | translate }}</mat-label>
          <input [formControl]="userNameControl"
                 autocomplete="username"
                 readonly="readonly"
                 onfocus="this.removeAttribute('readonly');"
                 type="text"
                 class="full-width"
                 matInput>
          <mat-error>
            {{userNameControl.hasError('required') ? ('LOGIN.usernameRequired' | translate) : ''}}
            {{userNameControl.hasError('minlength') ? ('LOGIN.minLength' | translate:{min: 4}) : ''}}
          </mat-error>
        </mat-form-field>

        <mat-form-field class="dialog__field" appearance="outline">
          <mat-label>{{ 'LOGIN.password' | translate }}</mat-label>
          <input [formControl]="passwordControl"
                 autocomplete="current-password"
                 readonly="readonly"
                 onfocus="this.removeAttribute('readonly');"
                 type="password"
                 class="full-width"
                 matInput>
          <mat-error>
            {{passwordControl.hasError('required') ? ('LOGIN.passwordRequired' | translate) : ''}}
            {{passwordControl.hasError('minlength') ? ('LOGIN.minLength' | translate:{min: 4}) : ''}}
          </mat-error>
        </mat-form-field>
        <div class="dialog__area" [ngClass]="{'mini': passwordControl.invalid && passwordControl.touched}">
          <a class="dialog__link" [routerLink]="['/auth/forgotpassword']">
            {{ 'LOGIN.forgotPassword' | translate }}
          </a>

          <div class="dialog__error" [hidden]="!errorMsg">
            <mat-error [hidden]="!errorMsg">{{errorMsg}}</mat-error>
          </div>
        </div>
      </div>

      <div class="dialog__footer">
        <a mat-stroked-button class="dialog__link dialog__keys mat-button-md" color="primary" mat-ripple [routerLink]="['/auth/managekeys']">
          {{ 'LOGIN.keysManagement' | translate }}
        </a>
        <button
          mat-flat-button
          (click)="onSubmit(form.value)"
          [disabled]="!form.valid"
          color="primary"
          class="dialog__submit mat-button-md">
          {{ 'LOGIN.submit' | translate }}
        </button>
      </div>

    </form>
  </mat-card>
</div>
