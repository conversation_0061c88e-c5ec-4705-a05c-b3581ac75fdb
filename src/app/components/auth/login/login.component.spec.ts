import { ComponentFixture, TestBed, waitForAsync } from '@angular/core/testing';
import { LoginComponent } from './login.component';
import { TranslateModule } from '@ngx-translate/core';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { NO_ERRORS_SCHEMA } from '@angular/core';
import { RouterTestingModule } from '@angular/router/testing';
import { HttpClientTestingModule } from '@angular/common/http/testing';
import { AuthService } from '../../../services/auth/auth.service';
import { DexieService } from '../../../services/dexie.service';
import { RedirectService } from '../../../services/redirect.service';
import { of, throwError } from 'rxjs';
import {
  SwHubAuthService,
  SwHubConfigService,
  SwHubInitService,
  SwuiNotificationsService
} from '@skywind-group/lib-swui';

describe('Login', () => {
  let component: LoginComponent;
  let fixture: ComponentFixture<LoginComponent>;
  let authService: AuthService;
  let redirectService: RedirectService;

  const credentialsMocks = {
    invalid: {
      username: 'Foo',
      password: 'b4r'
    },
    validNonReal: {
      username: 'Mario',
      password: 'Bows3r_Sucks'
    }
  };

  const responseMocks = {
    loginSuccess: {
      // tslint:disable-next-line:max-line-length
      accessToken: 'eyJhbGciOiJIUzUxMiIsInR5cCI6IkpXVCJ9.eyJ1c2VySWQiOjk5OTk5OSwiZW50aXR5SWQiOjk5OTksInVzZXJuYW1lIjoiRXhhbXBsZSIsImdyYW50ZWRQZXJtaXNzaW9ucyI6W10sImlhdCI6MTU1MzY3NzEwNSwiZXhwIjoxNTUzNjgwNzA1LCJpc3MiOiJza3l3aW5kZ3JvdXAifQ.lb2UyiOt_610HTnZP1m23ZBi6lzd-_5O9NoDTil_lTtGLXzj9-X3-xOu1H1OUwRtRdMcv9WkY_AqOxlJ6GPFCg',
      key: '75792645-cb49-41ff-9d6a-72e7245d63e1',
      username: 'promotionUser',
      lastPasswordUpdate: '2019-03-06T12:03:00.000Z',
      grantedPermissions: null
    },
    invalidPassword: {
      code: 202,
      message: 'User or Password does not match'
    }
  };

  beforeEach(waitForAsync(() => {

    TestBed.configureTestingModule({
      declarations: [LoginComponent],
      providers: [
        RedirectService,
        DexieService,
        AuthService,
        { provide: SwHubConfigService, useValue: {} },
        {
          provide: SwHubAuthService, useValue: {
            isLogged() {
              return false;
            }
          }
        },
        { provide: SwHubInitService, useValue: {} },
        { provide: SwuiNotificationsService, useValue: {} },
      ],
      schemas: [NO_ERRORS_SCHEMA],
      imports: [
        HttpClientTestingModule,
        RouterTestingModule,
        TranslateModule.forRoot(),
        ReactiveFormsModule,
        FormsModule,
      ]
    }).compileComponents();
  }));

  beforeEach(() => {
    redirectService = TestBed.inject(RedirectService);
    authService = TestBed.inject(AuthService);

    fixture = TestBed.createComponent(LoginComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });


  it('should create', () => {
    expect(component).toBeTruthy();
  });

  describe('form', () => {

    afterEach(() => {
      component.form.reset();
    });

    it('form invalid when empty', () => {
      expect(component.form.valid).toBeFalsy();
    });

    it('username should be required', () => {
      const errors = component.form.get('username').errors || {};
      expect(errors['required']).toBeTruthy();
    });

    it('password should be required', () => {
      const errors = component.form.get('password').errors || {};
      expect(errors['required']).toBeTruthy();
    });

    it('username min length validation is 4 chars', () => {
      component.form.get('username').setValue(credentialsMocks.invalid.username);
      const errors = component.form.get('username').errors || {};
      expect(errors['minlength']['requiredLength']).toBe(4);
      expect(errors['minlength']['actualLength']).toBeLessThan(4);
    });

    it('password min length validation is 4 chars', () => {
      component.form.get('password').setValue(credentialsMocks.invalid.password);
      const errors = component.form.get('password').errors || {};
      expect(errors['minlength']['requiredLength']).toBe(4);
      expect(errors['minlength']['actualLength']).toBeLessThan(4);
    });
  });

  xit('should show errorMsg on auth fail', waitForAsync(() => {
    component.form.setValue(credentialsMocks.validNonReal);
    spyOn(authService, 'login').and.returnValue(throwError(responseMocks.invalidPassword));
    component.onSubmit(component.form.getRawValue());

    fixture.whenStable().then(() => {
      fixture.detectChanges();
      expect(component.errorMsg).toBe('User or Password does not match');
    });
  }));

  xit('should call login on submit', waitForAsync(() => {
    spyOn(authService, 'login').and.returnValue(of(responseMocks.loginSuccess));
    component.form.setValue(credentialsMocks.validNonReal);
    component.onSubmit(component.form.getRawValue());

    fixture.whenStable().then(() => {
      fixture.detectChanges();
      expect(authService.login).toHaveBeenCalledWith(credentialsMocks.validNonReal);
    });
  }));

  xit('should call authorize on success', waitForAsync(() => {
    spyOn(authService, 'login').and.returnValue(of(responseMocks.loginSuccess));
    spyOn(authService, 'authorize');
    component.form.setValue(credentialsMocks.validNonReal);
    component.onSubmit(component.form.getRawValue());

    fixture.whenStable().then(() => {
      fixture.detectChanges();
      expect(authService.authorize).toHaveBeenCalledWith(responseMocks.loginSuccess);
    });
  }));

  xit('should call navigateToLastVisitedPage on success', waitForAsync(() => {
    spyOn(authService, 'login').and.returnValue(of(responseMocks.loginSuccess));
    spyOn(redirectService, 'navigate');
    component.form.setValue(credentialsMocks.validNonReal);
    component.onSubmit(component.form.getRawValue());

    fixture.whenStable().then(() => {
      fixture.detectChanges();
      expect(redirectService.navigate).toHaveBeenCalled();
    });
  }));

  xit('should redirect to 2fa login on special response', waitForAsync(() => {
    // spyOn(authService, 'login').and.returnValue(of(responseMocks.loginSuccess));
    // spyOn(authService, 'twoFactorRequired').and.returnValue(true);
    // const navigateSpy = spyOn(router, 'navigate');
    //
    // component.form.setValue(credentialsMocks.validNonReal);
    // component.onSubmit(component.form.getRawValue());
    //
    // fixture.whenStable().then(() => {
    //   fixture.detectChanges();
    //   expect(navigateSpy).toHaveBeenCalledWith(['auth/login/two-factor']);
    // });
  }));

  xit('should redirect to 2fa setup on special response', waitForAsync(() => {
    // spyOn(authService, 'login').and.returnValue(throwError('should fail to setup 2fa'));
    // spyOn(authService, 'twoFactorSetupRequired').and.returnValue(true);
    // const navigateSpy = spyOn(router, 'navigate');
    //
    // component.form.setValue(credentialsMocks.validNonReal);
    // component.onSubmit(component.form.getRawValue());
    //
    // fixture.whenStable().then(() => {
    //   fixture.detectChanges();
    //   expect(navigateSpy).toHaveBeenCalledWith(['auth/login/two-factor/setup']);
    // });
  }));

  xit('should navigate to change password if password expired', waitForAsync(() => {
    // spyOn(authService, 'login').and.returnValue(throwError('should fail to change password'));
    // spyOn(authService, 'passwordChangeRequired').and.returnValue(true);
    // const navigateSpy = spyOn(router, 'navigate');
    //
    // component.form.setValue(credentialsMocks.validNonReal);
    // component.onSubmit(component.form.getRawValue());
    //
    // fixture.whenStable().then(() => {
    //   fixture.detectChanges();
    //   expect(navigateSpy).toHaveBeenCalledWith(['auth/changepassword']);
    // });
  }));

});
