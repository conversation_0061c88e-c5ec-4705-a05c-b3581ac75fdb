.login  {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  width: 100%;
}

.dialog {
  width: 360px;
  margin-bottom: 10vh;
  &__form {
    width: 100%;
  }
  &__logo {
    width: 265px;
    margin: 0 auto 20px;
    img {
      display: block;
      height: auto;
      max-height: 100px;
      max-width: 100%;
      margin: auto;
    }
  }
  &__title {
    text-align: center;
  }
  &__field {
    width: 100%;
  }
  &__header {
    padding: 16px 16px 0;
  }
  &__body {
    padding: 0 16px;
  }
  &__footer {
    display: flex;
    padding: 16px;
    gap: 16px;
  }
  &__submit {
    display: block;
    flex: 1;
    min-width: auto !important;
  }
  &__area {
    position: relative;
    margin-top: -10px;
    height: 60px;
    &.mini {
      padding-top: 18px;
    }
  }
  &__error {
    display: flex;
    align-items: center;
    height: 100%;
  }
  &__link {
    display: flex;
    font-size: 14px;
    line-height: 16px;
    font-weight: 500;
    color: #1468CF;
    text-decoration: none;
    transition: color 0.15s ease-in-out;
    border-radius: 4px;
    overflow: hidden;
    &:hover {
      color: #333758;
    }
  }
  .keys {
    flex: 1;
  }
}
