import { Injectable } from '@angular/core';
import { CanActivate, Router } from '@angular/router';
import { AuthService } from '../../services/auth/auth.service';
import { AppStorage } from '../../lib/storage';
import { STORAGE_ACCESS_KEY_ACTIVE } from '../../app.constants';
import { RedirectService } from '../../services/redirect.service';
import { map } from 'rxjs/operators';
import { SwHubConfigService } from '@skywind-group/lib-swui';
import { Observable, of } from 'rxjs';

@Injectable()
export class LoginGuard implements CanActivate {
  private readonly refreshToken$: Observable<boolean>;

  constructor( { oauthClientId }: SwHubConfigService,
               private readonly auth: AuthService,
               private readonly redirect: RedirectService,
               private readonly router: Router ) {
    this.refreshToken$ = oauthClientId ? this.auth.refreshToken() : of(true);
  }

  canActivate() {
    return this.refreshToken$.pipe(
      map(() => {
        if (this.auth.isLogged()) {
          this.redirect.navigate();
          return false;
        }
        if (AppStorage.getItem(STORAGE_ACCESS_KEY_ACTIVE)) {
          return true;
        }
        return this.router.createUrlTree(['auth/managekeys']);
      })
    );
  }
}
