import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import { SwuiNotificationsModule, SwuiTopMenuModule } from '@skywind-group/lib-swui';
import { AuthService } from '../../services/auth/auth.service';
import { DexieService } from '../../services/dexie.service';
import { AuthComponent } from './auth.component';
import { AuthRouting } from './auth.routing';


@NgModule({
  imports: [
    CommonModule,
    AuthRouting,
    SwuiNotificationsModule.forRoot(),
    SwuiTopMenuModule
  ],
  declarations: [
    AuthComponent,
  ],
  providers: [
    AuthService,
    DexieService
  ],
  exports: []
})
export class AuthModule {
}
