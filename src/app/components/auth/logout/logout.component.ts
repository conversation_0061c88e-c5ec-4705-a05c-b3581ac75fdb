import { Component, ViewEncapsulation } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { AuthService, ERRCODE_TOKEN_ALREADY_EXPIRED } from '../../../services/auth/auth.service';
import { SwuiNotificationsService } from '@skywind-group/lib-swui';


@Component({
  selector: 'sw-hub-base-logout',
  encapsulation: ViewEncapsulation.None,
  template: '',
})
export class LogoutComponent {
  private logoutReasonCode: number;

  constructor(
    private auth: AuthService,
    private route: ActivatedRoute,
    private notifications: SwuiNotificationsService,
  ) {
    this.showLogoutNotification();
    this.auth.logout(this.isTokenLogoutRequired());
  }

  private showLogoutNotification() {
    const reason = this.getLogoutReasonCode();
    if (reason === ERRCODE_TOKEN_ALREADY_EXPIRED) {
      this.notifications.error('Access Session is expired');
    }
  }

  private isTokenLogoutRequired(): boolean {
    return this.getLogoutReasonCode() !== ERRCODE_TOKEN_ALREADY_EXPIRED;
  }

  private getLogoutReasonCode(): number {
    if (typeof this.logoutReasonCode === 'undefined') {
      this.logoutReasonCode = 0;
    }

    if ('reason' in this.route.snapshot.queryParams) {
      this.logoutReasonCode = parseInt(this.route.snapshot.queryParams['reason'], 10);
    }

    return this.logoutReasonCode;
  }
}
