.forgot {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  width: 100%;

  &__dialog {
    margin-bottom: 10vh;
  }

  &__field {
    width: 300px;
  }

  &__form {
    margin-top: 4px;
  }

  &__header {
    display: flex;
    justify-content: center;
  }

  &__desc {
    width: 300px;
    opacity: .8;
    font-size: 12px;
    line-height: 16px;
    text-align: center;
    margin-bottom: 16px;
  }

  &__actions {
    display: flex;
    justify-content: space-between;
    gap: 16px;
    margin: 0 !important;
    button {
      flex: 1;
    }
  }

  mat-card-content {
    margin-bottom: 0;
  }

  &__image {
    position: relative;
    display: flex;
    justify-content: center;
    width: 200px;
    margin: 0 auto 18px;

    img {
      display: block;
      width: 100%;
    }
  }

  &__refresh-button {
    padding: 20px 0 0 10px;
  }
}
