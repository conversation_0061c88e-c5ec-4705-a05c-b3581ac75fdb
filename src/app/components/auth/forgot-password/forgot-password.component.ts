import { Component, OnD<PERSON>roy } from '@angular/core';
import { FormControl, Validators } from '@angular/forms';
import { Router } from '@angular/router';
import { TranslateService } from '@ngx-translate/core';
import { SwuiNotificationsService } from '@skywind-group/lib-swui';
import { Subject } from 'rxjs';
import { take, takeUntil } from 'rxjs/operators';
import { AuthService } from '../../../services/auth/auth.service';
import { CaptchaInfo, PasswordResetData, PasswordResetResponse } from '../../../models/user.model';

@Component({
  selector: 'sw-hub-base-forgot-password',
  templateUrl: './forgot-password.component.html',
  styleUrls: ['./forgot-password.component.scss']
})
export class ForgotPasswordComponent implements OnDestroy {
  captcha?: CaptchaInfo;

  readonly identifierControl = new FormControl('', [
    Validators.required,
    Validators.minLength(4)
  ]);
  readonly captchaToken = new FormControl('', Validators.required);

  private readonly destroyed = new Subject<void>();

  constructor( private readonly auth: AuthService,
               private readonly router: Router,
               private readonly notificationsService: SwuiNotificationsService,
               private readonly translate: TranslateService ) {
  }

  ngOnDestroy(): void {
    this.destroyed.next();
    this.destroyed.complete();
  }

  onSubmit( event: Event ) {
    event.preventDefault();
    if (this.identifierControl.valid) {
      const data: PasswordResetData = {
        identifier: this.identifierControl.value,
        secretKey: this.auth.keyManagerGetActiveKey().key,
        captchaToken: this.captchaToken.value,
        csrfToken: this.captcha?.csrfToken
      };
      this.auth.resetPassword(data).pipe(
        take(1),
        takeUntil(this.destroyed)
      ).subscribe({
        next: ({ extraData }) => {
          this.captcha = extraData;
          this.notificationsService.success(this.translate.instant('FORGOTPASSWORD.success-notification'), '');
        },
        error: (error) => {
          this.notificationsService.error(error.error?.message || 'An error occurred', '');
          if (error.error?.extraData) {
            this.captcha = error.error.extraData;
          }
        }
      });
    }
  }

  onRefreshCaptcha( event: Event ) {
    event.preventDefault();
    this.captchaToken.setValue(null);
    this.auth.refreshCaptcha().pipe(
      take(1),
      takeUntil(this.destroyed),
    ).subscribe(captcha => {
      this.captcha = captcha;
    });
  }

  toLogin() {
    this.router.navigate(['auth']);
  }
}
