<div class="forgot">
  <mat-card class="forgot__dialog">
    <mat-card-header class="forgot__header">
      <mat-card-title>{{'FORGOTPASSWORD.title' | translate}}</mat-card-title>
    </mat-card-header>
    <mat-card-content>
      <div class="forgot__desc">{{ 'FORGOTPASSWORD.desc' | translate }}</div>
      <form class="forgot__form">
        <mat-form-field appearance="outline" class="forgot__field">
          <mat-label>{{'FORGOTPASSWORD.username' | translate}}</mat-label>
          <input matInput [formControl]="identifierControl">
          <mat-error>
            {{'VALIDATION.required' | translate}}
          </mat-error>
        </mat-form-field>
      </form>

      <div *ngIf="captcha">
        <div class="forgot__image">
          <img [src]="captcha?.image">
          <button class="forgot__refresh-button" mat-icon-button (click)="onRefreshCaptcha($event)">
            <mat-icon fontSet="material-icons-outline">refresh</mat-icon>
          </button>
        </div>
        <mat-form-field appearance="outline" class="forgot__field">
          <mat-label>{{'FORGOTPASSWORD.captchaPlaceholder' | translate}}</mat-label>
          <input matInput [formControl]="captchaToken">
          <mat-error>
            {{'VALIDATION.required' | translate}}
          </mat-error>
        </mat-form-field>
      </div>
    </mat-card-content>
    <mat-card-actions class="forgot__actions">
      <button mat-stroked-button color="primary" (click)="toLogin()" class="mat-button-md">
        {{'FORGOTPASSWORD.to-login' | translate}}
      </button>
      <button mat-flat-button color="primary" class="mat-button-md"
              [disabled]="!identifierControl.valid || (captcha && !captchaToken.valid)"
              (click)="onSubmit($event)">
        {{'FORGOTPASSWORD.submit' | translate}}
      </button>
    </mat-card-actions>
  </mat-card>
</div>
