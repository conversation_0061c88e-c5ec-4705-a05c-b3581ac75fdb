import { HttpClientTestingModule } from '@angular/common/http/testing';
import { NO_ERRORS_SCHEMA } from '@angular/core';
import { ComponentFixture, TestBed, waitForAsync } from '@angular/core/testing';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { BrowserDynamicTestingModule } from '@angular/platform-browser-dynamic/testing';
import { NoopAnimationsModule } from '@angular/platform-browser/animations';
import { Router } from '@angular/router';
import { RouterTestingModule } from '@angular/router/testing';
import { TranslateModule } from '@ngx-translate/core';
import { SwHubAuthService, SwHubInitService, SwuiNotificationsService } from '@skywind-group/lib-swui';
import { AuthService } from '../../../services/auth/auth.service';
import { DexieService } from '../../../services/dexie.service';
import { ConfirmDeleteKeyComponent } from './confirm-delete-key/confirm-delete-key.component';
import { KeysManagerComponent } from './keys-manager.component';
import { keysManagerMatModules } from './keys-manager.module';

describe('KeysManager', () => {

  let component: KeysManagerComponent;
  let fixture: ComponentFixture<KeysManagerComponent>;
  let dexieService: DexieService;
  let authService: AuthService;
  let router: Router;

  const keyDataMocks = {
    invalid: {
      label: 'Invalid Key',
      key: 'aaa11200'
    },
    valid: {
      label: 'Valid key',
      key: 'aaa11200-19f1-48c1-a78c-3a3d56095f38',
    },
    sample_01: {
      label: 'Valid key 001',
      key: 'aaa11200-19f1-48c1-a78c-3a3d56095001',
    },
    sample_02: {
      label: 'Valid key 002',
      key: 'aaa11200-19f1-48c1-a78c-3a3d56095002',
    },
    sample_03: {
      label: 'Valid key 003',
      key: 'aaa11200-19f1-48c1-a78c-3a3d56095003',
    },
  };
  const addedKeysMock = [
    {
      id: 1,
      selected: false,
      label: 'Valid key 001',
      key: 'aaa11200-19f1-48c1-a78c-3a3d56095001',
    },
    {
      id: 2,
      selected: false,
      label: 'Valid key 002',
      key: 'aaa11200-19f1-48c1-a78c-3a3d56095002',
    },
    {
      id: 3,
      selected: false,
      label: 'Valid key 003',
      key: 'aaa11200-19f1-48c1-a78c-3a3d56095003',
    },
  ];

  beforeEach(waitForAsync(() => {

    TestBed.configureTestingModule({
      declarations: [
        KeysManagerComponent,
        ConfirmDeleteKeyComponent,
      ],
      providers: [
        AuthService,
        DexieService,
        { provide: SwHubAuthService, useValue: {} },
        { provide: SwHubInitService, useValue: {} },
        { provide: SwuiNotificationsService, useValue: {} },
      ],
      schemas: [NO_ERRORS_SCHEMA],
      imports: [
        NoopAnimationsModule,
        HttpClientTestingModule,
        RouterTestingModule,
        TranslateModule.forRoot(),
        ReactiveFormsModule,
        FormsModule,
        ...keysManagerMatModules,
      ]
    }).overrideModule(BrowserDynamicTestingModule, {
      set: {
        entryComponents: [
          ConfirmDeleteKeyComponent,
        ]
      }
    }).compileComponents();
  }));

  beforeEach(() => {
    dexieService = TestBed.inject(DexieService);
    authService = TestBed.inject(AuthService);
    router = TestBed.inject(Router);

    spyOn(authService, 'keyManagerSetActiveKey');
    spyOn(dexieService, 'keyManagerAddKey').and.returnValue(Promise.resolve(1));
    spyOn(dexieService, 'keyManagerGetKeys').and.returnValue(Promise.resolve(addedKeysMock));

    fixture = TestBed.createComponent(KeysManagerComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  afterEach(() => {
    component.form.reset();
  });

  const addNewValidKey = ( data: { key: string, label: string } ) => {
    component.form.setValue(data);
    component.onAppendKey(component.form.value);
  };

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  describe('form', () => {
    it('form invalid when empty', () => {
      expect(component.form.valid).toBeFalsy();
    });

    it('key field is required', () => {
      const errors = component.form.get('key').errors || {};
      expect(errors['required']).toBeTruthy();
    });

    it('key min length validation is 10 chars', () => {
      component.form.get('key').setValue(keyDataMocks.invalid.key);
      const errors = component.form.get('key').errors || {};

      expect(errors['minLength']['min']).toBe(10, 'required length is 10');
      expect(component.form.get('key').value.length).toBeLessThan(10, 'actual length less than 10');
    });
  });

  it('should save added key', waitForAsync(() => {
    addNewValidKey(keyDataMocks.valid);

    fixture.whenStable().then(() => {
      // fixture.detectChanges();
      expect(authService.keyManagerSetActiveKey).toHaveBeenCalledWith(keyDataMocks.valid);
      expect(dexieService.keyManagerAddKey).toHaveBeenCalledWith(keyDataMocks.valid);
    });
  }));

  it('should select added key', () => {
    addNewValidKey(keyDataMocks.valid);
    expect(component.activeKey.key).toBe(keyDataMocks.valid.key);
  });

  it('key can be selected', waitForAsync(() => {
    addNewValidKey(keyDataMocks.sample_02);

    fixture.whenStable().then(() => {
      // fixture.detectChanges();
      const key = component.items.find(item => item.key === keyDataMocks.sample_02.key);
      component.setKeyActive(key);

      expect(component.activeKey.key).toBe(keyDataMocks.sample_02.key, 'second added key has been selected');
    });
  }));

  it('key can be deleted', waitForAsync(() => {
    spyOn(authService, 'keyManagerRemoveActiveKey');
    const removeKeySpy = spyOn(dexieService, 'keyManagerRemoveKey').and.returnValue(Promise.resolve());

    fixture.whenStable().then(() => {
      // fixture.detectChanges();

      const keyToDelete = component.items.find(item => item.key === keyDataMocks.sample_02.key);
      expect(keyToDelete).toBeTruthy();
      component.handleDeleteKey(keyToDelete);
      expect(component.itemToDelete).toBe(keyToDelete, 'itemToDelete should be sample_02');
      component.deleteKey();
      expect(removeKeySpy).toHaveBeenCalledWith(component.itemToDelete);
    });
  }));

  it('key can be activated for login', waitForAsync(() => {
    const navigateSpy = spyOn(router, 'navigate');

    fixture.whenStable().then(() => {
      const secondKey = component.items.find(item => item.id === addedKeysMock[1].id);
      component.setKeyActive(secondKey);
      component.onActivateKey(component.activeKey);

      expect(authService.keyManagerSetActiveKey).toHaveBeenCalledWith(secondKey);
      expect(navigateSpy).toHaveBeenCalledWith(['/auth/login']);
    });

  }));
});
