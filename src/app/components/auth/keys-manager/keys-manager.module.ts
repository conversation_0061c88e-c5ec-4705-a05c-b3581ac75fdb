import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { KeysManagerComponent } from './keys-manager.component';
import { TranslateModule } from '@ngx-translate/core';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { KeysManagerRouting } from './keys-manager.routing';
import { ConfirmDeleteKeyComponent } from './confirm-delete-key/confirm-delete-key.component';
import { MatIconModule } from '@angular/material/icon';
import { MatDialogModule } from '@angular/material/dialog';
import { MatCardModule } from '@angular/material/card';
import { MatInputModule } from '@angular/material/input';
import { MatExpansionModule } from '@angular/material/expansion';
import { MatButtonModule } from '@angular/material/button';
import { MatRadioModule } from '@angular/material/radio';
import { SwuiControlMessagesModule } from '@skywind-group/lib-swui';

export const keysManagerMatModules = [
  MatInputModule,
  MatButtonModule,
  MatIconModule,
  MatRadioModule,
  MatDialogModule,
  MatExpansionModule,
  MatCardModule,
];

@NgModule({
    imports: [
        CommonModule,
        TranslateModule,
        ReactiveFormsModule,
        FormsModule,
        KeysManagerRouting,
        ...keysManagerMatModules,
        SwuiControlMessagesModule,
    ],
  declarations: [
    KeysManagerComponent,
    ConfirmDeleteKeyComponent,
  ],
  entryComponents: [
    ConfirmDeleteKeyComponent,
  ]
})
export class KeysManagerModule {
}
