import { Component, Inject, OnInit } from '@angular/core';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';
import { IDexieLogin<PERSON>ey } from '../../../../models/dexie.model';

export interface ConfirmDeleteDialogData {
  key: IDexie<PERSON>ogin<PERSON>ey;
}

@Component({
  selector: 'sw-hub-base-confirm-delete-key',
  templateUrl: './confirm-delete-key.component.html',
  styleUrls: ['./confirm-delete-key.component.scss']
})
export class ConfirmDeleteKeyComponent implements OnInit {

  constructor(
    public dialogRef: MatDialogRef<ConfirmDeleteKeyComponent>,
    @Inject(MAT_DIALOG_DATA) public data: ConfirmDeleteDialogData
  ) {

  }


  ngOnInit() {
  }

  onNoClick(): void {
    this.dialogRef.close();
  }
}
