<div class="login">

  <div class="login__dialog key-dialog">
    <mat-card class="key-dialog__header">
      <div class="key-dialog__logo">
        <img [src]="logo" alt="{{ 'skywindFalconLogoAlt' | translate }}">
      </div>

      <h2 class="mat-title key-dialog__title">{{ 'MANAGEKEYS.title' | translate }}</h2>
    </mat-card>

    <div class="key-dialog__body">
      <mat-accordion>

        <mat-expansion-panel [expanded]="(!activeKey && items) || !items">
          <mat-expansion-panel-header>
            <mat-panel-title>
              {{ 'MANAGEKEYS.add' | translate }}
            </mat-panel-title>
          </mat-expansion-panel-header>

          <form [formGroup]="form" (ngSubmit)="onAppendKey(form.value)">
            <mat-form-field class="full-width" appearance="outline">
              <mat-label>{{ 'MANAGEKEYS.Label' | translate }}</mat-label>
              <input matInput [formControl]="labelField" type="text">
            </mat-form-field>

            <mat-form-field class="full-width" appearance="outline">
              <mat-label>{{ 'MANAGEKEYS.accessKey' | translate }}</mat-label>
              <input matInput [formControl]="keyField" type="text">
              <mat-error>
                <lib-swui-control-messages
                  [messages]="messageErrors"
                  [control]="keyField">
                </lib-swui-control-messages>
              </mat-error>
            </mat-form-field>
            <div class="key-dialog__actions key-dialog__actions--new">
              <button
                mat-stroked-button
                color="primary"
                [disabled]="!items?.length"
                (click)="toLogin()"
                class="key-dialog__back mat-button-md">
                {{'FORGOTPASSWORD.to-login' | translate}}
              </button>
              <button
                mat-flat-button
                [disabled]="!form.valid"
                type="submit"
                color="primary"
                class="full-width mat-button-md key-dialog__submit">
                {{ 'MANAGEKEYS.submit' | translate }}
              </button>
            </div>
          </form>
        </mat-expansion-panel>

        <mat-expansion-panel [expanded]="activeKey && items">
          <mat-expansion-panel-header>
            <mat-panel-title>
              {{ 'MANAGEKEYS.selectKey' | translate }}
            </mat-panel-title>

            <mat-panel-description>
              <span *ngIf="items.length; else noKeys">
                {{ 'MANAGEKEYS.numberOfSavedKeys' | translate : { length: items.length } }}
              </span>
              <ng-template #noKeys>
                <span>{{ 'MANAGEKEYS.noKeysToSelect' | translate }}</span>
              </ng-template>
            </mat-panel-description>
          </mat-expansion-panel-header>
          <div class="keys">
            <mat-radio-group [(ngModel)]="activeKey" class="keys__list">

                <mat-radio-button
                  *ngFor="let item of items"
                  [value]="item"
                  [checked]="isActiveKey(item)"
                  (click)="setKeyActive(item)"
                  class="keys__item">

                  <div class="keys__field">
                    <div class="keys__value">
                      {{item.key}}
                    </div>
                    <div class="keys__label">{{item.label}}</div>
                    <mat-icon class="keys__delete" (click)="handleDeleteKey(item)">
                      delete_outlined
                    </mat-icon>
                  </div>
                </mat-radio-button>

            </mat-radio-group>
          </div>
          <div class="key-dialog__actions">
            <button
              mat-stroked-button
              color="primary"
              [disabled]="!items?.length"
              (click)="toLogin()"
              class="key-dialog__back mat-button-md">
              {{'FORGOTPASSWORD.to-login' | translate}}
            </button>

            <button
              mat-flat-button
              [disabled]="!activeKey"
              type="submit"
              color="primary"
              class="mat-button-md key-dialog__submit"
              (click)="onActivateKey(activeKey)">
              {{ 'MANAGEKEYS.processedWithSelectedKey' | translate }}
            </button>
          </div>

        </mat-expansion-panel>

      </mat-accordion>
    </div>
  </div>

</div>
