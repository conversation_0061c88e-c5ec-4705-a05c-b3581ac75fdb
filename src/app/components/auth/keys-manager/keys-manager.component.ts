import { Component, ViewEncapsulation } from '@angular/core';
import { AbstractControl, FormBuilder, FormGroup, Validators } from '@angular/forms';
import { Router } from '@angular/router';
import { IDexieLoginKey } from '../../../models/dexie.model';
import { AuthService } from '../../../services/auth/auth.service';
import { DexieService } from '../../../services/dexie.service';
import { MatDialog } from '@angular/material/dialog';
import { ConfirmDeleteKeyComponent } from './confirm-delete-key/confirm-delete-key.component';
import { filter, map, takeUntil } from 'rxjs/operators';
import { Subject } from 'rxjs';
import { ValidationService } from '../../../services/validation.service';
import { SwHubConfigService } from '@skywind-group/lib-swui';

@Component({
  selector: 'sw-hub-base-keys-manager',
  encapsulation: ViewEncapsulation.None,
  templateUrl: './keys-manager.component.html',
  styleUrls: ['./keys-manager.component.scss']
})
export class KeysManagerComponent {
  readonly logo: string;

  messageErrors = {
    required: 'VALIDATION.required',
    minLength: 'VALIDATION.minLength'
  };

  form: FormGroup;
  labelField: AbstractControl;
  keyField: AbstractControl;
  activeKey: IDexieLoginKey;
  items: IDexieLoginKey[] = [];
  itemToDelete: IDexieLoginKey;

  private readonly destroyed$ = new Subject<any>();

  constructor( private router: Router,
               private fb: FormBuilder,
               private dialog: MatDialog,
               protected auth: AuthService,
               protected dexieService: DexieService,
               { logo }: SwHubConfigService
  ) {
    this.logo = logo?.solo ?? 'img/logo-skywind-solo.png';
    this.initForm();
    this.update();
  }

  ngOnDestroy(): void {
    this.destroyed$.next();
    this.destroyed$.complete();
  }

  initForm() {
    this.form = this.fb.group({
      'label': ['', []],
      'key': ['', [Validators.required, ValidationService.minLength(10)]],
    });
    this.labelField = this.form.controls['label'];
    this.keyField = this.form.controls['key'];

    this.keyField.valueChanges.pipe(
      filter(keyFieldValue => !!keyFieldValue),
      map(( keyFieldValue: string ) => keyFieldValue?.replace(/\s+/g, '')),
      takeUntil(this.destroyed$)
    ).subscribe(( keyFieldValue: string ) => {
      this.keyField.patchValue(keyFieldValue, { emitEvent: false });
    });
  }

  onAppendKey( data ): void {
    this.auth.keyManagerSetActiveKey(data);
    this.dexieService.keyManagerAddKey(data).then(() => this.update());
    this.activeKey = data;
    this.form.reset();
  }

  deleteKey( key: IDexieLoginKey = this.itemToDelete ): void {
    this.activeKey = key;
    this.auth.keyManagerRemoveActiveKey();
    this.dexieService.keyManagerRemoveKey(key).then(() => this.update());
  }

  onActivateKey( key: IDexieLoginKey ): void {
    if (key && key.key) {
      this.auth.keyManagerSetActiveKey(key);
      this.router.navigate(['/auth/login']);
    }
  }

  handleDeleteKey( item ) {
    this.itemToDelete = item;
    const dialogRef = this.dialog.open(ConfirmDeleteKeyComponent, {
      width: '400px',
      data: { key: item }
    });

    dialogRef.afterClosed().subscribe(( key ) => {
      if (key) {
        this.deleteKey(key);
      }
    });
  }

  update() {
    this.dexieService.keyManagerGetKeys().then(data => {
      this.items = data;
      this.activeKey = this.auth.keyManagerGetActiveKey();
      if (!this.activeKey) {
        if (this.items.length) {
          this.activeKey = this.items[0];
          this.auth.keyManagerSetActiveKey(this.items[0]);
        } else {
          this.activeKey = undefined;
          this.auth.keyManagerRemoveActiveKey();
        }
      }
      if (this.items.length) {
        this.activeKey = this.items.find(item => item.key === this.activeKey.key);
      }
    });
  }

  isActiveKey( key: IDexieLoginKey ): boolean {
    return this.activeKey && (key.id === this.activeKey.id);
  }

  setKeyActive( item: IDexieLoginKey ) {
    this.activeKey = item;
  }

  toLogin() {
    this.router.navigate(['auth']);
  }
}
