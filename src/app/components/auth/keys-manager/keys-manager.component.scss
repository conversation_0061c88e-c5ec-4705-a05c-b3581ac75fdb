mat-form-field.full-width,
button.full-width {
  width: 100%
}

.keys-list-wrapper {
  height: 200px;
  overflow: auto;
}

.keys {
  margin: 0 -24px;
  width: calc(100% + 48px);
  max-height: 200px;
  overflow: auto;
  &::-webkit-scrollbar-track {
    background: rgba(0,0,0,.03);
    width: 4px;
  }

  &::-webkit-scrollbar {
    width: 4px;
    background: rgba(0,0,0,.03);
  }

  &::-webkit-scrollbar-thumb {
    border-radius: 2px;
    background-color: rgba(0,0,0,.33);
  }
  &__field {
    position: relative;
    width: 318px;
    padding-right: 34px;
  }
  &__label {
    position: absolute;
    top: -18px;
    left: 0;
    font-size: 12px;
    color: rgba(0,0,0,.54)
  }
  &__value {
    width: 100%;
    height: 24px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    font-size: 14px;
  }
  &__delete {
    position: absolute;
    right: 10px;
    top: 0;
    cursor: pointer;
  }

  &__item {
    width: 100%;
    padding: 20px 10px 10px;
    border-bottom: 1px solid rgba(0, 0, 0, .12);
    overflow: hidden;
  }
}

.login  {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  width: 100%;
  overflow: auto;
}

.key-dialog {
  width: 360px;
  &__logo {
    width: 265px;
    margin: 0 auto 20px;
    img {
      display: block;
      height: auto;
      max-height: 100px;
      max-width: 100%;
      margin: auto;
    }
  }
  &__title {
    text-align: center;
  }
  &__header {
    padding: 16px;
    margin-bottom: 16px;
  }
  &__body {
    padding: 0;
  }
  &__actions {
    display: flex;
    gap: 16px;
    padding: 16px 8px;
    &--new {
      padding-top: 10px;
    }
  }
  &__submit {
    flex: 1;
  }
}
