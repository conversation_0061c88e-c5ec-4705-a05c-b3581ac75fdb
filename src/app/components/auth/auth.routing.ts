import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { SwHubAuthGuard } from '@skywind-group/lib-swui';
import { AuthComponent } from './auth.component';
import { LoginGuard } from './login.guard';

export const routes: Routes = [
  {
    path: '',
    component: AuthComponent,
    canActivate: [SwHubAuthGuard],
    children: [
      {
        path: '',
        redirectTo: 'login',
        pathMatch: 'full'
      },
      {
        path: 'login',
        canActivate: [LoginGuard],
        loadChildren: () => import('./login/login.module').then(m => m.LoginModule),
        data: {
          title: 'Login'
        }
      },
      {
        path: 'login/two-factor',
        loadChildren: () => import('./two-factor/two-factor.module').then(m => m.TwoFactorModule),
        data: {
          title: '2FA'
        }
      },
      {
        path: 'logout',
        loadChildren: () => import('./logout/logout.module').then(m => m.LogoutModule),
        data: {
          title: 'Logout'
        }
      },
      {
        path: 'managekeys',
        loadChildren: () => import('./keys-manager/keys-manager.module').then(m => m.KeysManagerModule),
        data: {
          title: 'Manage Keys'
        }
      },
      {
        path: 'changepassword',
        loadChildren: () => import('./change-password/change-password.module').then(m => m.ChangePasswordModule),
        data: {
          title: 'Change Password'
        }
      },
      {
        path: 'resetpassword',
        loadChildren: () => import('./reset-password/reset-password.module').then(m => m.ResetPasswordModule),
        data: {
          title: 'Reset Password'
        }
      },
      {
        path: 'forgotpassword',
        loadChildren: () => import('./forgot-password/forgot-password.module').then(m => m.ForgotPasswordModule),
        data: {
          title: 'Forgot Password'
        }
      },
      {
        path: 'twofactorsettings',
        loadChildren: () => import('./two-factor-settings/two-factor-settings.module').then(m => m.TwoFactorSettingsModule),
        data: {
          title: '2FA Settings'
        }
      }
    ]
  }
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  providers: [LoginGuard],
  exports: [RouterModule]
})
export class AuthRouting {
}
