import { Injectable } from '@angular/core';
import { CanActivate, Router } from '@angular/router';
import { SwHubAuthService } from '@skywind-group/lib-swui';
import { map, take } from 'rxjs/operators';

@Injectable()
export class EmptyGuard implements CanActivate {

  constructor( private readonly auth: SwHubAuthService, private readonly router: Router ) {
  }

  canActivate() {
    if (this.auth.isLogged()) {
      return true;
    }
    return this.auth.logged.pipe(
      take(1),
      map(() => true),
      map(() => this.auth.isLogged() ? true : this.router.parseUrl('auth/login'))
    );
  }
}
