import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { EmptyComponent } from './empty.component';
import { SwuiTopMenuModule } from '@skywind-group/lib-swui';
import { RouterModule, Routes } from '@angular/router';
import { EmptyGuard } from './empty.guard';


const routes: Routes = [
  {
    path: '',
    pathMatch: 'full',
    component: EmptyComponent,
    canActivate: [EmptyGuard],
  }
];

@NgModule({
  imports: [
    CommonModule,
    RouterModule.forChild(routes),
    SwuiTopMenuModule,
  ],
  providers: [EmptyGuard],
  declarations: [EmptyComponent],
})
export class EmptyModule {
}
