export interface UserLoginRequest {
  secretKey: string;
  username: string;
  password: string;
}

export interface LoginInfo {
  key?: string;
  username: string;
  accessToken: string;
  grantedPermissions: string;
  twoFactor?: boolean;
}

export interface UserCredentials {
  username: string;
  password?: string;
  newPassword?: string;
  rememberme?: boolean;
  token?: string;
}

export interface PasswordResetData {
  identifier: string;
  secretKey: string;
  captchaToken?: string;
  csrfToken?: string;
}

export interface CaptchaInfo {
  csrfToken: string;
  image: string;
}

export interface PasswordResetResponse {
  extraData?: CaptchaInfo;
}
