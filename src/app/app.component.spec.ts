import { ComponentFixture, TestBed, waitForAsync } from '@angular/core/testing';
import { RouterTestingModule } from '@angular/router/testing';
import { AppComponent } from './app.component';
import { JwtModule } from '@auth0/angular-jwt';
import { HttpClientTestingModule } from '@angular/common/http/testing';
import { TranslateModule } from '@ngx-translate/core';
import { RedirectService } from './services/redirect.service';
import { SwBrowserTitleService, SwHubAuthService, SwHubConfigService, SwHubInitService } from '@skywind-group/lib-swui';

export function tokenGetter() {
  return '';
}

describe('AppComponent', () => {
  let fixture: ComponentFixture<AppComponent>;
  let app: AppComponent;

  beforeEach(waitForAsync(() => {
    TestBed.configureTestingModule({
      imports: [
        HttpClientTestingModule,
        RouterTestingModule,
        TranslateModule.forRoot(),
        JwtModule.forRoot({
          config: {
            tokenGetter: tokenGetter,
            headerName: 'X-ACCESS-TOKEN',
            authScheme: '',
          }
        }),
      ],
      declarations: [
        AppComponent
      ],
      providers: [
        SwBrowserTitleService,
        { provide: RedirectService, useValue: {} },
        { provide: SwHubAuthService, useValue: {} },
        { provide: SwHubConfigService, useValue: {} },
        { provide: SwHubInitService, useValue: {} },
      ]
    }).compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(AppComponent);
    app = fixture.debugElement.componentInstance;
  });

  it('should create the app', () => {
    expect(app).toBeTruthy();
  });
});
