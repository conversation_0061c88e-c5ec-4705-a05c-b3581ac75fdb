services:

  sw-ubo-hub-base:
    build:
      context: .
      dockerfile: Dockerfile
    ports:
      - "8880:80"
    environment:
      - NODE_ENV=production
      - MANAGEMENT_API=api-bo.gms-server.dev-qa.ss211208.com # api:3000
      - OAUTH_API=api-bo.gms-server.dev-qa.ss211208.com
      - BRIDGE_URL=http://api.cd.d.skywind-tech.com:8880/bridge
      - LOGIN_URL=http://api.cd.d.skywind-tech.com:8880/auth/login
      - LOGOUT_URL=http://api.cd.d.skywind-tech.com:8880/auth/logout
      - CASINO_HUB_URL=http://api.cd.d.skywind-tech.com:8881
      - DATA_HUB_URL=http://api.cd.d.skywind-tech.com:8881/bi
      - ENGAGEMENT_HUB_URL=http://api.cd.d.skywind-tech.com:8882
      - SOFTGATE_HOST=localhost
      - SOFTGATE_BRIDGE_URL=localhost
      - SOFTGATE_LOGIN_URL=localhost
      - SOFTGATE_CASINO_HUB_URL=localhost
      - SOFTGATE_ENGAGEMENT_HUB_URL=localhost
      - SOFTGATE_DATA_HUB_URL=localhost
      - SOFTGATE_STUDIO_HUB_URL=localhost
