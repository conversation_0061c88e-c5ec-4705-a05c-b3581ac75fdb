#!/usr/bin/env bash

version=$(node -p -e "require('./package.json').version")
revision=$(git rev-parse --short HEAD)
datetime=$(git log -1 --format=%cd HEAD)
branch=${GIT_BRANCH##origin/}
build=${BUILD_NUMBER:-0}

config=src/environments/base.ts

echo 'export const env = JSON.parse(`{' >  ${config}
echo '  "version": "'${version}'",' >> ${config}
echo '  "build": "'${build}'",' >> ${config}
echo '  "git": {' >> ${config}
echo '    "revision": "'${revision}'",' >> ${config}
echo '    "datetime": "'${datetime}'",' >> ${config}
echo '    "branch": "'${branch}'"' >> ${config}
echo '  }' >> ${config}
echo '}`);' >> ${config}
