#!/bin/sh

if [ -z "${SOFTGATE_HOST}" ]; then
  export SOFTGATE_HOST='unknown'
fi

envsubst '$MANAGEMENT_API $OAUTH_API $OAUTH_CLIENT_ID $BRIDGE_URL $LOGIN_URL $LOGOUT_URL $CASINO_HUB_URL $ENGAGEMENT_HUB_URL $DATA_HUB_URL $STUDIO_HUB_URL $SOFTGATE_HOST $SOFTGATE_BRIDGE_URL $SOFTGATE_LOGIN_URL $SOFTGATE_LOGOUT_URL $SOFTGATE_CASINO_HUB_URL $SOFTGATE_ENGAGEMENT_HUB_URL $SOFTGATE_DATA_HUB_URL $SOFTGATE_STUDIO_HUB_URL $ENV_NAME $LOCATION_NAME' < /usr/share/nginx/template.conf > /etc/nginx/conf.d/default.conf
nginx -g "daemon off;"
