upstream upstream_api {
  server ${MANAGEMENT_API} fail_timeout=30s max_fails=100;
}

upstream upstream_oauth {
  server ${OAUTH_API} fail_timeout=30s max_fails=100;
}

server {
  listen 80 reuseport default_server;
  server_name  localhost;

  userid_name 'uid';
  userid_path '/; HttpOnly';
  userid_expires 365d;
  userid         on;
  server_tokens off;

  gzip on;
  gzip_comp_level 3;
  gzip_static on;
  gzip_min_length 2048;
  gzip_buffers      16 8k;
  gzip_vary         on;
  gzip_types text/plain text/css text/xml text/javascript application/x-javascript application/javascript application/xml
  image/svg+xml application/xml+rss image/x-icon image/bmp;
  gzip_disable "msie6";

  root /usr/share/nginx/html/;

  proxy_buffer_size 128k;
  proxy_buffers 4 256k;
  proxy_busy_buffers_size 256k;

  large_client_header_buffers 4 32k;
  client_max_body_size 32m;

  proxy_connect_timeout 300;
  proxy_send_timeout 300;
  proxy_read_timeout 300;
  send_timeout 300;
  expires -1;
  etag off;

  location /favicon.ico {
    if ($host = $SOFTGATE_HOST) {
      rewrite ^ /img/softgate/favicon.ico break;
    }
    rewrite ^ /favicon.ico break;
  }

  location / {
    index /index.html;
    try_files $uri $uri/ /index.html =404;
    access_log off;
  }

  location /api/config {
    default_type application/json;
    set $ENV_CONFIG '';
    set $ENV_CONFIG '${ENV_CONFIG} {';
    set $ENV_CONFIG '${ENV_CONFIG} "host": "${host}",';
    if ($host = $SOFTGATE_HOST) {
      set $ENV_CONFIG '${ENV_CONFIG} "bridge": "${SOFTGATE_BRIDGE_URL}",';
      set $ENV_CONFIG '${ENV_CONFIG} "loginUrl": "${SOFTGATE_LOGIN_URL}",';
      set $ENV_CONFIG '${ENV_CONFIG} "logoutUrl": "${SOFTGATE_LOGOUT_URL}",';
      set $ENV_CONFIG '${ENV_CONFIG} "hubs": {';
      set $ENV_CONFIG '${ENV_CONFIG}   "casino": "${SOFTGATE_CASINO_HUB_URL}",';
      set $ENV_CONFIG '${ENV_CONFIG}   "engagement": "${SOFTGATE_ENGAGEMENT_HUB_URL}",';
      set $ENV_CONFIG '${ENV_CONFIG}   "analytics": "${SOFTGATE_DATA_HUB_URL}",';
      set $ENV_CONFIG '${ENV_CONFIG}   "studio": "${SOFTGATE_STUDIO_HUB_URL}"';
      set $ENV_CONFIG '${ENV_CONFIG} },';
      set $ENV_CONFIG '${ENV_CONFIG} "logo": {';
      set $ENV_CONFIG '${ENV_CONFIG}   "main": "/img/softgate/logo.png",';
      set $ENV_CONFIG '${ENV_CONFIG}   "solo": "/img/softgate/logo.png",';
      set $ENV_CONFIG '${ENV_CONFIG}   "white": ""';
      set $ENV_CONFIG '${ENV_CONFIG} },';
    }
    if ($host != $SOFTGATE_HOST) {
      set $ENV_CONFIG '${ENV_CONFIG} "bridge": "${BRIDGE_URL}",';
      set $ENV_CONFIG '${ENV_CONFIG} "loginUrl": "${LOGIN_URL}",';
      set $ENV_CONFIG '${ENV_CONFIG} "logoutUrl": "${LOGOUT_URL}",';
      set $ENV_CONFIG '${ENV_CONFIG} "hubs": {';
      set $ENV_CONFIG '${ENV_CONFIG} "casino": "${CASINO_HUB_URL}",';
      set $ENV_CONFIG '${ENV_CONFIG} "engagement": "${ENGAGEMENT_HUB_URL}",';
      set $ENV_CONFIG '${ENV_CONFIG} "analytics": "${DATA_HUB_URL}",';
      set $ENV_CONFIG '${ENV_CONFIG} "studio": "${STUDIO_HUB_URL}"';
      set $ENV_CONFIG '${ENV_CONFIG} },';
    }
    set $ENV_CONFIG '${ENV_CONFIG} "envName": "${ENV_NAME}",';
    set $ENV_CONFIG '${ENV_CONFIG} "oauthClientId": "${OAUTH_CLIENT_ID}",';
    set $ENV_CONFIG '${ENV_CONFIG} "locationName": "${LOCATION_NAME}"';
    set $ENV_CONFIG '${ENV_CONFIG} }';
    return 200 $ENV_CONFIG;
  }

  location /v1 {
    proxy_pass http://upstream_api/v1;
  }

  location /oauth {
    proxy_pass http://upstream_oauth/v1/oauth;
  }
}
