## compiled output
/dist
/tmp
/out-tsc
# Only exists if <PERSON><PERSON> was run
/bazel-out

## dependencies
/node_modules
package-lock.json

# config
.env

## profiling files
chrome-profiler-events.json
speed-measure-plugin.json

## IDEs and editors
/.idea
.project
.classpath
.c9/
*.launch
.settings/
*.sublime-workspace

## IDE - VSCode
.vscode/*
!.vscode/settings.json
!.vscode/tasks.json
!.vscode/launch.json
!.vscode/extensions.json
.history/*

## misc
/.sass-cache
/connect.lock
/coverage
/libpeerconnection.log
npm-debug.log
yarn-error.log
testem.log
/typings

## System Files
.DS_Store
Thumbs.db
